# Backend Operations Documentation
## AmILearning Platform Backend Architecture

### Overview
The AmILearning platform uses Supabase as the primary backend service, providing PostgreSQL database, authentication, real-time subscriptions, and API management. This document outlines all backend operations, database design, and service integrations.

---

## Architecture Overview

### Technology Stack
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **Real-time**: Supabase Realtime
- **API Layer**: Supabase Auto-generated APIs
- **External APIs**: OpenAI GPT-3.5-turbo
- **File Storage**: Supabase Storage (future implementation)

### Service Architecture
```
Frontend (React) 
    ↓
Supabase Client SDK
    ↓
Supabase Services
    ├── PostgreSQL Database
    ├── Authentication Service
    ├── Real-time Subscriptions
    └── Row Level Security
    ↓
External APIs
    └── OpenAI API
```

---

## Database Operations

### Core Tables

#### 1. Profiles Table
**Purpose**: Extended user profile information beyond Supabase Auth

```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  points INTEGER DEFAULT 0,
  preferences JSONB DEFAULT '{
    "darkMode": false,
    "focusMode": false,
    "autoplay": true,
    "notifications": true,
    "openaiModel": "gpt-3.5-turbo",
    "openaiMaxTokens": 500
  }'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Operations**:
- `getCurrentUser()`: Fetch user profile with badges
- `updateUserProfile()`: Update user information and preferences
- `createProfile()`: Auto-created via trigger on user registration

#### 2. Videos Table
**Purpose**: Store video metadata and content information

```sql
CREATE TABLE videos (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  url TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT DEFAULT '',
  thumbnail TEXT,
  duration INTEGER DEFAULT 0,
  tags TEXT[] DEFAULT '{}',
  playlist_id UUID REFERENCES playlists(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Operations**:
- `getUserVideos()`: Fetch all videos for authenticated user with related data
- `addVideo()`: Create new video with progress tracking
- `updateVideo()`: Update video metadata
- `deleteVideo()`: Remove video and all related data

#### 3. Video Progress Table
**Purpose**: Track individual video learning progress

```sql
CREATE TABLE video_progress (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE NOT NULL,
  status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
  watch_time INTEGER DEFAULT 0,
  completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
  last_watched TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, video_id)
);
```

**Operations**:
- `updateVideoProgress()`: Real-time progress updates during video playback
- `getVideoProgress()`: Fetch progress for specific video
- `getUserProgress()`: Get overall learning statistics

#### 4. AI Summaries Table
**Purpose**: Store AI-generated content summaries

```sql
CREATE TABLE ai_summaries (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  key_points TEXT[] DEFAULT '{}',
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(video_id)
);
```

**Operations**:
- `createSummary()`: Store AI-generated summary
- `getSummary()`: Retrieve summary for video
- `regenerateSummary()`: Update existing summary

#### 5. Quizzes & Quiz Attempts Tables
**Purpose**: Interactive learning assessment system

```sql
CREATE TABLE quizzes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE NOT NULL,
  questions JSONB NOT NULL DEFAULT '[]',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(video_id)
);

CREATE TABLE quiz_attempts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  quiz_id UUID REFERENCES quizzes(id) ON DELETE CASCADE NOT NULL,
  user_answers JSONB NOT NULL DEFAULT '[]',
  score INTEGER DEFAULT 0 CHECK (score >= 0 AND score <= 100),
  completed_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Operations**:
- `createQuiz()`: Store AI-generated quiz questions
- `submitQuizAttempt()`: Record user quiz completion
- `getQuizResults()`: Retrieve quiz performance data

---

## Authentication Operations

### User Registration
```typescript
async signUp(email: string, password: string, name: string) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: { name: name }
    }
  });
  return { data, error };
}
```

### User Login
```typescript
async signIn(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  return { data, error };
}
```

### Session Management
```typescript
async getCurrentUser() {
  const { data: { user }, error } = await supabase.auth.getUser();
  return { user, error };
}

onAuthStateChange(callback) {
  return supabase.auth.onAuthStateChange(callback);
}
```

---

## Row Level Security (RLS) Policies

### User Data Isolation
All tables implement RLS to ensure users can only access their own data:

```sql
-- Example: Videos table policies
CREATE POLICY "Users can read own videos"
  ON videos FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own videos"
  ON videos FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);
```

### Security Features
- **Data Isolation**: Users cannot access other users' data
- **Authentication Required**: All operations require valid authentication
- **Automatic User Association**: Foreign keys automatically link to authenticated user
- **Cascade Deletion**: User deletion removes all associated data

---

## External API Integrations

### OpenAI API Integration

#### Configuration
```typescript
const openAIConfig = {
  apiKey: user.preferences.openaiApiKey || process.env.VITE_OPENAI_API_KEY,
  model: user.preferences.openaiModel || 'gpt-3.5-turbo',
  maxTokens: user.preferences.openaiMaxTokens || 500
};
```

#### Summary Generation
```typescript
async generateVideoSummary(videoUrl: string, title: string): Promise<AISummary> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are an adaptive learning assistant...'
        },
        {
          role: 'user',
          content: `Analyze this video titled "${title}" (URL: ${videoUrl})...`
        }
      ],
      max_tokens: maxTokens
    })
  });
}
```

#### Quiz Generation
```typescript
async generateQuiz(videoUrl: string, title: string): Promise<Quiz> {
  // Similar structure to summary generation
  // Returns structured quiz questions with multiple choice options
}
```

#### Cost Optimization
- **Token Limits**: Configurable max tokens per request
- **Caching**: Store generated content to avoid regeneration
- **Fallback**: Graceful degradation when API unavailable
- **Rate Limiting**: Prevent excessive API usage

---

## Real-time Operations

### Progress Tracking
```typescript
// Real-time video progress updates
const subscription = supabase
  .channel('video_progress')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'video_progress',
    filter: `user_id=eq.${userId}`
  }, (payload) => {
    // Update UI with new progress
  })
  .subscribe();
```

### Live Updates
- **Progress Tracking**: Real-time video progress updates
- **Achievement Notifications**: Instant badge/point updates
- **Playlist Changes**: Live playlist modifications
- **User Preferences**: Immediate settings synchronization

---

## Performance Optimizations

### Database Indexing
```sql
-- Performance indexes
CREATE INDEX idx_videos_user_id ON videos(user_id);
CREATE INDEX idx_videos_playlist_id ON videos(playlist_id);
CREATE INDEX idx_video_progress_user_id ON video_progress(user_id);
CREATE INDEX idx_video_progress_video_id ON video_progress(video_id);
```

### Query Optimization
- **Selective Queries**: Only fetch required fields
- **Join Optimization**: Efficient table joins for related data
- **Pagination**: Limit large result sets
- **Caching**: Client-side caching for frequently accessed data

### Connection Pooling
- **Supabase Pooling**: Automatic connection management
- **Connection Limits**: Configured for optimal performance
- **Timeout Handling**: Graceful handling of slow queries

---

## Error Handling & Monitoring

### Error Categories
1. **Database Errors**: Connection issues, constraint violations
2. **Authentication Errors**: Invalid credentials, expired sessions
3. **API Errors**: OpenAI rate limits, network failures
4. **Validation Errors**: Invalid input data, missing fields

### Error Handling Strategy
```typescript
try {
  const result = await databaseOperation();
  return result;
} catch (error) {
  console.error('Database operation failed:', error);
  
  // Categorize error type
  if (error.code === 'PGRST301') {
    // Handle RLS policy violation
  } else if (error.code === '23505') {
    // Handle unique constraint violation
  }
  
  // Return user-friendly error
  throw new Error('Operation failed. Please try again.');
}
```

### Monitoring & Logging
- **Supabase Dashboard**: Built-in monitoring and analytics
- **Error Tracking**: Comprehensive error logging
- **Performance Metrics**: Query performance monitoring
- **Usage Analytics**: API usage and cost tracking

---

## Backup & Recovery

### Automated Backups
- **Daily Backups**: Automatic database backups via Supabase
- **Point-in-Time Recovery**: Restore to specific timestamps
- **Cross-Region Replication**: Data redundancy for reliability

### Data Export
```typescript
// User data export functionality
async exportUserData(userId: string) {
  const [profile, videos, playlists, progress] = await Promise.all([
    getUserProfile(userId),
    getUserVideos(userId),
    getUserPlaylists(userId),
    getUserProgress(userId)
  ]);
  
  return {
    profile,
    videos,
    playlists,
    progress,
    exportedAt: new Date().toISOString()
  };
}
```

---

## Security Measures

### Data Protection
- **Encryption**: Data encrypted in transit and at rest
- **API Key Security**: Secure storage and rotation
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries via Supabase

### Access Control
- **Role-Based Access**: User role management
- **API Rate Limiting**: Prevent abuse and DoS attacks
- **CORS Configuration**: Secure cross-origin requests
- **Session Management**: Secure session handling

### Compliance
- **GDPR Compliance**: User data rights and privacy
- **Data Retention**: Configurable data retention policies
- **Audit Logging**: Comprehensive activity logging
- **Privacy Controls**: User data anonymization options

---

## Deployment & Infrastructure

### Environment Configuration
```bash
# Production Environment Variables
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_OPENAI_API_KEY=your-openai-key
VITE_OPENAI_MODEL=gpt-3.5-turbo
VITE_OPENAI_MAX_TOKENS=500
```

### Migration Management
```sql
-- Migration file structure
-- /supabase/migrations/YYYYMMDDHHMMSS_description.sql

-- Example migration
/*
  # Add new feature table
  
  1. New Tables
    - feature_table with columns
  2. Security
    - Enable RLS
    - Add policies
*/

CREATE TABLE feature_table (...);
ALTER TABLE feature_table ENABLE ROW LEVEL SECURITY;
-- Add policies...
```

### Scaling Considerations
- **Database Scaling**: Vertical and horizontal scaling options
- **Connection Pooling**: Optimized for high concurrency
- **CDN Integration**: Static asset delivery optimization
- **Caching Strategy**: Multi-layer caching implementation

---

## API Documentation Reference

For detailed API endpoint documentation, see [API_Documentation.md](./API_Documentation.md).

For database schema details, see [Schemas.md](./Schemas.md).

---

**Last Updated**: December 2024  
**Version**: 1.0  
**Next Review**: January 2025