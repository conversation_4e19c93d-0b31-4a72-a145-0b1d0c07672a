# API Documentation - AmILearning Platform
## Comprehensive API Reference

### Overview
The AmILearning platform uses Supabase's auto-generated REST API along with custom service functions. This document provides detailed information about all available endpoints, authentication, and integration patterns.

---

## Base Configuration

### API Base URL
```
https://your-project-id.supabase.co/rest/v1/
```

### Authentication
All API requests require authentication via Supabase Auth:

```typescript
// Headers for authenticated requests
{
  'Authorization': 'Bearer <user_jwt_token>',
  'apikey': '<supabase_anon_key>',
  'Content-Type': 'application/json'
}
```

### Response Format
All API responses follow this structure:
```typescript
interface APIResponse<T> {
  data?: T;
  error?: {
    message: string;
    details?: string;
    hint?: string;
    code?: string;
  };
}
```

---

## Authentication Endpoints

### 1. User Registration
**Endpoint**: `POST /auth/v1/signup`

**Description**: Register a new user account

**Request Body**:
```typescript
{
  email: string;
  password: string;
  data?: {
    name: string;
  };
}
```

**Response**:
```typescript
{
  user: {
    id: string;
    email: string;
    created_at: string;
    user_metadata: {
      name: string;
    };
  };
  session: {
    access_token: string;
    refresh_token: string;
    expires_in: number;
  } | null;
}
```

**Example**:
```typescript
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'securepassword',
  options: {
    data: {
      name: 'John Doe'
    }
  }
});
```

### 2. User Login
**Endpoint**: `POST /auth/v1/token?grant_type=password`

**Description**: Authenticate existing user

**Request Body**:
```typescript
{
  email: string;
  password: string;
}
```

**Response**:
```typescript
{
  user: User;
  session: Session;
}
```

**Example**:
```typescript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'securepassword'
});
```

### 3. User Logout
**Endpoint**: `POST /auth/v1/logout`

**Description**: Sign out current user

**Example**:
```typescript
const { error } = await supabase.auth.signOut();
```

### 4. Get Current User
**Endpoint**: `GET /auth/v1/user`

**Description**: Get current authenticated user

**Response**:
```typescript
{
  user: User | null;
}
```

**Example**:
```typescript
const { data: { user }, error } = await supabase.auth.getUser();
```

---

## User Profile Endpoints

### 1. Get User Profile
**Endpoint**: `GET /profiles?id=eq.{user_id}`

**Description**: Retrieve user profile information

**Response**:
```typescript
{
  id: string;
  email: string;
  name: string;
  points: number;
  preferences: {
    darkMode: boolean;
    focusMode: boolean;
    autoplay: boolean;
    notifications: boolean;
    openaiModel: string;
    openaiMaxTokens: number;
    openaiApiKey?: string;
  };
  created_at: string;
  updated_at: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('profiles')
  .select('*')
  .eq('id', user.id)
  .single();
```

### 2. Update User Profile
**Endpoint**: `PATCH /profiles?id=eq.{user_id}`

**Description**: Update user profile information

**Request Body**:
```typescript
{
  name?: string;
  points?: number;
  preferences?: Partial<UserPreferences>;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('profiles')
  .update({
    name: 'Updated Name',
    preferences: {
      darkMode: true,
      focusMode: false
    }
  })
  .eq('id', user.id);
```

---

## Video Management Endpoints

### 1. Get User Videos
**Endpoint**: `GET /videos?user_id=eq.{user_id}`

**Description**: Retrieve all videos for authenticated user

**Query Parameters**:
- `select`: Specify columns to return
- `order`: Sort order
- `limit`: Limit number of results
- `offset`: Pagination offset

**Response**:
```typescript
Array<{
  id: string;
  user_id: string;
  url: string;
  title: string;
  description: string;
  thumbnail: string | null;
  duration: number;
  tags: string[];
  playlist_id: string | null;
  created_at: string;
  updated_at: string;
}>
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('videos')
  .select(`
    *,
    video_progress(*),
    ai_summaries(*),
    quizzes(*),
    quiz_attempts(*)
  `)
  .eq('user_id', user.id)
  .order('created_at', { ascending: false });
```

### 2. Add New Video
**Endpoint**: `POST /videos`

**Description**: Add a new video to user's library

**Request Body**:
```typescript
{
  url: string;
  title: string;
  description?: string;
  thumbnail?: string;
  duration?: number;
  tags?: string[];
  playlist_id?: string;
}
```

**Response**:
```typescript
{
  id: string;
  user_id: string;
  url: string;
  title: string;
  description: string;
  thumbnail: string | null;
  duration: number;
  tags: string[];
  playlist_id: string | null;
  created_at: string;
  updated_at: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('videos')
  .insert({
    url: 'https://youtube.com/watch?v=example',
    title: 'Learning Video',
    description: 'Educational content',
    tags: ['programming', 'tutorial']
  })
  .select()
  .single();
```

### 3. Update Video
**Endpoint**: `PATCH /videos?id=eq.{video_id}&user_id=eq.{user_id}`

**Description**: Update video metadata

**Request Body**:
```typescript
{
  title?: string;
  description?: string;
  thumbnail?: string;
  duration?: number;
  tags?: string[];
  playlist_id?: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('videos')
  .update({
    title: 'Updated Title',
    tags: ['updated', 'tags']
  })
  .eq('id', videoId)
  .eq('user_id', user.id);
```

### 4. Delete Video
**Endpoint**: `DELETE /videos?id=eq.{video_id}&user_id=eq.{user_id}`

**Description**: Delete video and all related data

**Example**:
```typescript
const { error } = await supabase
  .from('videos')
  .delete()
  .eq('id', videoId)
  .eq('user_id', user.id);
```

---

## Video Progress Endpoints

### 1. Get Video Progress
**Endpoint**: `GET /video_progress?user_id=eq.{user_id}&video_id=eq.{video_id}`

**Description**: Get progress for specific video

**Response**:
```typescript
{
  id: string;
  user_id: string;
  video_id: string;
  status: 'not_started' | 'in_progress' | 'completed';
  watch_time: number;
  completion_percentage: number;
  last_watched: string;
  created_at: string;
  updated_at: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('video_progress')
  .select('*')
  .eq('user_id', user.id)
  .eq('video_id', videoId)
  .single();
```

### 2. Update Video Progress
**Endpoint**: `POST /video_progress` (upsert)

**Description**: Update or create video progress

**Request Body**:
```typescript
{
  user_id: string;
  video_id: string;
  status: 'not_started' | 'in_progress' | 'completed';
  watch_time: number;
  completion_percentage: number;
  last_watched: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('video_progress')
  .upsert({
    user_id: user.id,
    video_id: videoId,
    status: 'in_progress',
    watch_time: 1200,
    completion_percentage: 60,
    last_watched: new Date().toISOString()
  });
```

---

## Playlist Management Endpoints

### 1. Get User Playlists
**Endpoint**: `GET /playlists?user_id=eq.{user_id}`

**Description**: Retrieve all playlists for authenticated user

**Response**:
```typescript
Array<{
  id: string;
  user_id: string;
  name: string;
  description: string;
  color: string;
  created_at: string;
  updated_at: string;
}>
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('playlists')
  .select('*')
  .eq('user_id', user.id)
  .order('created_at', { ascending: false });
```

### 2. Create Playlist
**Endpoint**: `POST /playlists`

**Description**: Create a new playlist

**Request Body**:
```typescript
{
  name: string;
  description?: string;
  color?: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('playlists')
  .insert({
    name: 'My Learning Playlist',
    description: 'Collection of programming tutorials',
    color: '#3B82F6'
  })
  .select()
  .single();
```

### 3. Update Playlist
**Endpoint**: `PATCH /playlists?id=eq.{playlist_id}&user_id=eq.{user_id}`

**Description**: Update playlist information

**Request Body**:
```typescript
{
  name?: string;
  description?: string;
  color?: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('playlists')
  .update({
    name: 'Updated Playlist Name',
    color: '#10B981'
  })
  .eq('id', playlistId)
  .eq('user_id', user.id);
```

### 4. Delete Playlist
**Endpoint**: `DELETE /playlists?id=eq.{playlist_id}&user_id=eq.{user_id}`

**Description**: Delete playlist (videos are unassigned, not deleted)

**Example**:
```typescript
const { error } = await supabase
  .from('playlists')
  .delete()
  .eq('id', playlistId)
  .eq('user_id', user.id);
```

---

## AI Content Endpoints

### 1. Get Video Summary
**Endpoint**: `GET /ai_summaries?video_id=eq.{video_id}`

**Description**: Get AI-generated summary for video

**Response**:
```typescript
{
  id: string;
  video_id: string;
  content: string;
  key_points: string[];
  generated_at: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('ai_summaries')
  .select('*')
  .eq('video_id', videoId)
  .single();
```

### 2. Create Video Summary
**Endpoint**: `POST /ai_summaries`

**Description**: Store AI-generated summary

**Request Body**:
```typescript
{
  video_id: string;
  content: string;
  key_points: string[];
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('ai_summaries')
  .insert({
    video_id: videoId,
    content: 'AI-generated summary content...',
    key_points: ['Key point 1', 'Key point 2', 'Key point 3']
  });
```

### 3. Get Video Quiz
**Endpoint**: `GET /quizzes?video_id=eq.{video_id}`

**Description**: Get quiz questions for video

**Response**:
```typescript
{
  id: string;
  video_id: string;
  questions: Array<{
    id: string;
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
  }>;
  created_at: string;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('quizzes')
  .select('*')
  .eq('video_id', videoId)
  .single();
```

### 4. Create Quiz
**Endpoint**: `POST /quizzes`

**Description**: Store AI-generated quiz

**Request Body**:
```typescript
{
  video_id: string;
  questions: Array<{
    id: string;
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
  }>;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('quizzes')
  .insert({
    video_id: videoId,
    questions: [
      {
        id: 'q1',
        question: 'What is the main concept?',
        options: ['A', 'B', 'C', 'D'],
        correctAnswer: 1,
        explanation: 'Explanation here...'
      }
    ]
  });
```

---

## Quiz Attempt Endpoints

### 1. Get Quiz Attempts
**Endpoint**: `GET /quiz_attempts?user_id=eq.{user_id}&quiz_id=eq.{quiz_id}`

**Description**: Get user's quiz attempts

**Response**:
```typescript
Array<{
  id: string;
  user_id: string;
  quiz_id: string;
  user_answers: Array<{
    questionId: string;
    selectedAnswer: number;
    isCorrect: boolean;
  }>;
  score: number;
  completed_at: string;
  created_at: string;
}>
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('quiz_attempts')
  .select('*')
  .eq('user_id', user.id)
  .eq('quiz_id', quizId)
  .order('completed_at', { ascending: false });
```

### 2. Submit Quiz Attempt
**Endpoint**: `POST /quiz_attempts`

**Description**: Submit quiz answers and score

**Request Body**:
```typescript
{
  quiz_id: string;
  user_answers: Array<{
    questionId: string;
    selectedAnswer: number;
    isCorrect: boolean;
  }>;
  score: number;
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('quiz_attempts')
  .insert({
    quiz_id: quizId,
    user_answers: [
      {
        questionId: 'q1',
        selectedAnswer: 1,
        isCorrect: true
      }
    ],
    score: 85
  });
```

---

## Badge System Endpoints

### 1. Get User Badges
**Endpoint**: `GET /user_badges?user_id=eq.{user_id}`

**Description**: Get all badges earned by user

**Response**:
```typescript
Array<{
  id: string;
  user_id: string;
  badge_name: string;
  badge_description: string;
  badge_icon: string;
  badge_category: 'progress' | 'quiz' | 'streak' | 'time';
  earned_at: string;
  created_at: string;
}>
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('user_badges')
  .select('*')
  .eq('user_id', user.id)
  .order('earned_at', { ascending: false });
```

### 2. Award Badge
**Endpoint**: `POST /user_badges`

**Description**: Award badge to user

**Request Body**:
```typescript
{
  badge_name: string;
  badge_description: string;
  badge_icon: string;
  badge_category: 'progress' | 'quiz' | 'streak' | 'time';
}
```

**Example**:
```typescript
const { data, error } = await supabase
  .from('user_badges')
  .insert({
    badge_name: 'First Video',
    badge_description: 'Added your first video',
    badge_icon: 'play',
    badge_category: 'progress'
  });
```

---

## Real-time Subscriptions

### 1. Video Progress Updates
**Description**: Subscribe to real-time progress updates

**Example**:
```typescript
const subscription = supabase
  .channel('video_progress')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'video_progress',
    filter: `user_id=eq.${user.id}`
  }, (payload) => {
    console.log('Progress updated:', payload.new);
  })
  .subscribe();
```

### 2. Badge Notifications
**Description**: Subscribe to new badge awards

**Example**:
```typescript
const subscription = supabase
  .channel('user_badges')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'user_badges',
    filter: `user_id=eq.${user.id}`
  }, (payload) => {
    console.log('New badge earned:', payload.new);
  })
  .subscribe();
```

---

## External API Integrations

### 1. OpenAI API Integration

#### Generate Summary
**Service Function**: `generateVideoSummary()`

**Parameters**:
```typescript
{
  videoUrl: string;
  title: string;
  userPreferences?: {
    openaiApiKey?: string;
    openaiModel?: string;
    openaiMaxTokens?: number;
  };
}
```

**Response**:
```typescript
{
  id: string;
  content: string;
  keyPoints: string[];
  generatedAt: Date;
}
```

**Example**:
```typescript
const summary = await generateVideoSummary(
  'https://youtube.com/watch?v=example',
  'Video Title'
);
```

#### Generate Quiz
**Service Function**: `generateQuiz()`

**Parameters**:
```typescript
{
  videoUrl: string;
  title: string;
  userPreferences?: {
    openaiApiKey?: string;
    openaiModel?: string;
    openaiMaxTokens?: number;
  };
}
```

**Response**:
```typescript
{
  id: string;
  questions: Array<{
    id: string;
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
  }>;
}
```

**Example**:
```typescript
const quiz = await generateQuiz(
  'https://youtube.com/watch?v=example',
  'Video Title'
);
```

---

## Error Handling

### Common Error Codes
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource doesn't exist
- `409`: Conflict - Duplicate resource
- `422`: Unprocessable Entity - Validation error
- `429`: Too Many Requests - Rate limit exceeded
- `500`: Internal Server Error - Server error

### Error Response Format
```typescript
{
  error: {
    message: string;
    details?: string;
    hint?: string;
    code?: string;
  }
}
```

### Error Handling Example
```typescript
try {
  const { data, error } = await supabase
    .from('videos')
    .insert(videoData);
    
  if (error) {
    console.error('Database error:', error);
    throw new Error(`Failed to add video: ${error.message}`);
  }
  
  return data;
} catch (error) {
  console.error('Operation failed:', error);
  // Handle error appropriately
}
```

---

## Rate Limiting & Performance

### Supabase Limits
- **API Requests**: 500 requests per second (default)
- **Database Connections**: 60 concurrent connections
- **Storage**: 500MB (free tier)
- **Bandwidth**: 2GB (free tier)

### OpenAI Limits
- **GPT-3.5-turbo**: 3,500 requests per minute
- **Token Limits**: Configurable per request
- **Cost**: ~$0.002 per 1K tokens

### Best Practices
- **Caching**: Cache frequently accessed data
- **Pagination**: Use limit/offset for large datasets
- **Batch Operations**: Group multiple operations
- **Connection Pooling**: Reuse database connections

---

## SDK Integration Examples

### React Integration
```typescript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

// Custom hook for videos
function useVideos() {
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function fetchVideos() {
      const { data, error } = await supabase
        .from('videos')
        .select('*')
        .order('created_at', { ascending: false });
        
      if (error) {
        console.error('Error fetching videos:', error);
      } else {
        setVideos(data);
      }
      setLoading(false);
    }
    
    fetchVideos();
  }, []);
  
  return { videos, loading };
}
```

### TypeScript Types
```typescript
// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: Profile;
        Insert: Omit<Profile, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Profile, 'id'>>;
      };
      videos: {
        Row: Video;
        Insert: Omit<Video, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Video, 'id' | 'user_id'>>;
      };
      // ... other tables
    };
  };
}

// Typed client
const supabase = createClient<Database>(url, key);
```

---

**Last Updated**: December 2024  
**API Version**: 1.0  
**Next Review**: January 2025