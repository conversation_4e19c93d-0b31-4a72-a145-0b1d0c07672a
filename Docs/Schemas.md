# Database Schemas Documentation
## AmILearning Platform Database Design

### Overview
This document provides comprehensive documentation of all database schemas, tables, relationships, and constraints used in the AmILearning platform. The database is built on PostgreSQL via Supabase.

---

## Schema Overview

### Database Information
- **Database Type**: PostgreSQL 15+
- **Provider**: Supabase
- **Schema**: public
- **Extensions**: uuid-ossp
- **Character Set**: UTF-8
- **Timezone**: UTC

### Design Principles
- **Normalization**: 3NF compliance for data integrity
- **Security**: Row Level Security (RLS) on all tables
- **Performance**: Strategic indexing for query optimization
- **Scalability**: Designed for horizontal scaling
- **Audit Trail**: Created/updated timestamps on all tables

---

## Core Tables

### 1. Profiles Table
**Purpose**: Extended user profile information beyond Supabase Auth

```sql
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  points INTEGER DEFAULT 0,
  preferences JSONB DEFAULT '{
    "darkMode": false,
    "focusMode": false,
    "autoplay": true,
    "notifications": true,
    "openaiModel": "gpt-3.5-turbo",
    "openaiMaxTokens": 500
  }'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Columns**:
- `id` (UUID, PK): References auth.users(id), cascade delete
- `email` (TEXT, NOT NULL): User email address
- `name` (TEXT, NOT NULL): User display name
- `points` (INTEGER, DEFAULT 0): Gamification points
- `preferences` (JSONB): User preferences and settings
- `created_at` (TIMESTAMPTZ): Account creation timestamp
- `updated_at` (TIMESTAMPTZ): Last profile update timestamp

**Indexes**:
- Primary key on `id`
- Automatic trigger for `updated_at`

**Constraints**:
- Foreign key to `auth.users(id)` with CASCADE DELETE
- NOT NULL constraints on email and name

**RLS Policies**:
```sql
-- Users can read own profile
CREATE POLICY "Users can read own profile"
  ON profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Users can update own profile
CREATE POLICY "Users can update own profile"
  ON profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Users can insert own profile
CREATE POLICY "Users can insert own profile"
  ON profiles FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);
```

---

### 2. Videos Table
**Purpose**: Store video metadata and content information

```sql
CREATE TABLE videos (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  url TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT DEFAULT '',
  thumbnail TEXT,
  duration INTEGER DEFAULT 0,
  tags TEXT[] DEFAULT '{}',
  playlist_id UUID,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Columns**:
- `id` (UUID, PK): Unique video identifier
- `user_id` (UUID, FK, NOT NULL): Owner of the video
- `url` (TEXT, NOT NULL): Video URL (YouTube, Vimeo, direct)
- `title` (TEXT, NOT NULL): Video title
- `description` (TEXT): Video description
- `thumbnail` (TEXT): Thumbnail image URL
- `duration` (INTEGER): Video duration in seconds
- `tags` (TEXT[]): Array of tags for categorization
- `playlist_id` (UUID, FK): Optional playlist assignment
- `created_at` (TIMESTAMPTZ): Video addition timestamp
- `updated_at` (TIMESTAMPTZ): Last update timestamp

**Indexes**:
```sql
CREATE INDEX idx_videos_user_id ON videos(user_id);
CREATE INDEX idx_videos_playlist_id ON videos(playlist_id);
```

**Constraints**:
```sql
-- Foreign key to users
ALTER TABLE videos ADD CONSTRAINT videos_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Foreign key to playlists (nullable)
ALTER TABLE videos ADD CONSTRAINT fk_videos_playlist 
  FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE SET NULL;
```

**RLS Policies**:
```sql
-- Users can read own videos
CREATE POLICY "Users can read own videos"
  ON videos FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert own videos
CREATE POLICY "Users can insert own videos"
  ON videos FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update own videos
CREATE POLICY "Users can update own videos"
  ON videos FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can delete own videos
CREATE POLICY "Users can delete own videos"
  ON videos FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);
```

---

### 3. Playlists Table
**Purpose**: Organize videos into collections

```sql
CREATE TABLE playlists (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT DEFAULT '',
  color TEXT DEFAULT '#3B82F6',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Columns**:
- `id` (UUID, PK): Unique playlist identifier
- `user_id` (UUID, FK, NOT NULL): Playlist owner
- `name` (TEXT, NOT NULL): Playlist name
- `description` (TEXT): Playlist description
- `color` (TEXT): Hex color code for UI display
- `created_at` (TIMESTAMPTZ): Creation timestamp
- `updated_at` (TIMESTAMPTZ): Last update timestamp

**Indexes**:
```sql
CREATE INDEX idx_playlists_user_id ON playlists(user_id);
```

**Constraints**:
```sql
ALTER TABLE playlists ADD CONSTRAINT playlists_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
```

**RLS Policies**:
```sql
-- Users can read own playlists
CREATE POLICY "Users can read own playlists"
  ON playlists FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert own playlists
CREATE POLICY "Users can insert own playlists"
  ON playlists FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update own playlists
CREATE POLICY "Users can update own playlists"
  ON playlists FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can delete own playlists
CREATE POLICY "Users can delete own playlists"
  ON playlists FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);
```

---

### 4. Video Progress Table
**Purpose**: Track individual video learning progress

```sql
CREATE TABLE video_progress (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE NOT NULL,
  status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
  watch_time INTEGER DEFAULT 0,
  completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
  last_watched TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, video_id)
);
```

**Columns**:
- `id` (UUID, PK): Unique progress record identifier
- `user_id` (UUID, FK, NOT NULL): User tracking progress
- `video_id` (UUID, FK, NOT NULL): Video being tracked
- `status` (TEXT): Progress status (not_started, in_progress, completed)
- `watch_time` (INTEGER): Total watch time in seconds
- `completion_percentage` (INTEGER): Percentage completed (0-100)
- `last_watched` (TIMESTAMPTZ): Last viewing timestamp
- `created_at` (TIMESTAMPTZ): Initial tracking timestamp
- `updated_at` (TIMESTAMPTZ): Last progress update

**Indexes**:
```sql
CREATE INDEX idx_video_progress_user_id ON video_progress(user_id);
CREATE INDEX idx_video_progress_video_id ON video_progress(video_id);
```

**Constraints**:
```sql
-- Unique constraint for user-video combination
ALTER TABLE video_progress ADD CONSTRAINT video_progress_user_id_video_id_key 
  UNIQUE (user_id, video_id);

-- Check constraint for completion percentage
ALTER TABLE video_progress ADD CONSTRAINT video_progress_completion_percentage_check 
  CHECK (completion_percentage >= 0 AND completion_percentage <= 100);

-- Check constraint for status values
ALTER TABLE video_progress ADD CONSTRAINT video_progress_status_check 
  CHECK (status = ANY (ARRAY['not_started'::text, 'in_progress'::text, 'completed'::text]));

-- Foreign key constraints
ALTER TABLE video_progress ADD CONSTRAINT video_progress_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE video_progress ADD CONSTRAINT video_progress_video_id_fkey 
  FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE;
```

**RLS Policies**:
```sql
-- Users can read own video progress
CREATE POLICY "Users can read own video progress"
  ON video_progress FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert own video progress
CREATE POLICY "Users can insert own video progress"
  ON video_progress FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update own video progress
CREATE POLICY "Users can update own video progress"
  ON video_progress FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);
```

---

### 5. AI Summaries Table
**Purpose**: Store AI-generated content summaries

```sql
CREATE TABLE ai_summaries (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  key_points TEXT[] DEFAULT '{}',
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(video_id)
);
```

**Columns**:
- `id` (UUID, PK): Unique summary identifier
- `video_id` (UUID, FK, NOT NULL): Associated video
- `content` (TEXT, NOT NULL): Summary content
- `key_points` (TEXT[]): Array of key learning points
- `generated_at` (TIMESTAMPTZ): AI generation timestamp

**Constraints**:
```sql
-- Unique constraint - one summary per video
ALTER TABLE ai_summaries ADD CONSTRAINT ai_summaries_video_id_key 
  UNIQUE (video_id);

-- Foreign key to videos
ALTER TABLE ai_summaries ADD CONSTRAINT ai_summaries_video_id_fkey 
  FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE;
```

**RLS Policies**:
```sql
-- Users can read summaries for own videos
CREATE POLICY "Users can read summaries for own videos"
  ON ai_summaries FOR SELECT
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM videos 
    WHERE videos.id = ai_summaries.video_id 
    AND videos.user_id = auth.uid()
  ));

-- System can insert summaries
CREATE POLICY "System can insert summaries"
  ON ai_summaries FOR INSERT
  TO authenticated
  WITH CHECK (EXISTS (
    SELECT 1 FROM videos 
    WHERE videos.id = ai_summaries.video_id 
    AND videos.user_id = auth.uid()
  ));
```

---

### 6. Quizzes Table
**Purpose**: Store AI-generated quiz questions

```sql
CREATE TABLE quizzes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  video_id UUID REFERENCES videos(id) ON DELETE CASCADE NOT NULL,
  questions JSONB NOT NULL DEFAULT '[]',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(video_id)
);
```

**Columns**:
- `id` (UUID, PK): Unique quiz identifier
- `video_id` (UUID, FK, NOT NULL): Associated video
- `questions` (JSONB, NOT NULL): Array of quiz questions
- `created_at` (TIMESTAMPTZ): Quiz creation timestamp

**Question Structure (JSONB)**:
```json
[
  {
    "id": "uuid",
    "question": "What is the main concept?",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "correctAnswer": 1,
    "explanation": "Explanation of correct answer"
  }
]
```

**Constraints**:
```sql
-- Unique constraint - one quiz per video
ALTER TABLE quizzes ADD CONSTRAINT quizzes_video_id_key 
  UNIQUE (video_id);

-- Foreign key to videos
ALTER TABLE quizzes ADD CONSTRAINT quizzes_video_id_fkey 
  FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE;
```

**RLS Policies**:
```sql
-- Users can read quizzes for own videos
CREATE POLICY "Users can read quizzes for own videos"
  ON quizzes FOR SELECT
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM videos 
    WHERE videos.id = quizzes.video_id 
    AND videos.user_id = auth.uid()
  ));

-- System can insert quizzes
CREATE POLICY "System can insert quizzes"
  ON quizzes FOR INSERT
  TO authenticated
  WITH CHECK (EXISTS (
    SELECT 1 FROM videos 
    WHERE videos.id = quizzes.video_id 
    AND videos.user_id = auth.uid()
  ));
```

---

### 7. Quiz Attempts Table
**Purpose**: Record user quiz completion and performance

```sql
CREATE TABLE quiz_attempts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  quiz_id UUID REFERENCES quizzes(id) ON DELETE CASCADE NOT NULL,
  user_answers JSONB NOT NULL DEFAULT '[]',
  score INTEGER DEFAULT 0 CHECK (score >= 0 AND score <= 100),
  completed_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Columns**:
- `id` (UUID, PK): Unique attempt identifier
- `user_id` (UUID, FK, NOT NULL): User taking quiz
- `quiz_id` (UUID, FK, NOT NULL): Quiz being attempted
- `user_answers` (JSONB, NOT NULL): User's answers
- `score` (INTEGER): Calculated score (0-100)
- `completed_at` (TIMESTAMPTZ): Quiz completion timestamp
- `created_at` (TIMESTAMPTZ): Attempt start timestamp

**User Answers Structure (JSONB)**:
```json
[
  {
    "questionId": "uuid",
    "selectedAnswer": 1,
    "isCorrect": true
  }
]
```

**Indexes**:
```sql
CREATE INDEX idx_quiz_attempts_user_id ON quiz_attempts(user_id);
```

**Constraints**:
```sql
-- Score range constraint
ALTER TABLE quiz_attempts ADD CONSTRAINT quiz_attempts_score_check 
  CHECK (score >= 0 AND score <= 100);

-- Foreign key constraints
ALTER TABLE quiz_attempts ADD CONSTRAINT quiz_attempts_quiz_id_fkey 
  FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE;

ALTER TABLE quiz_attempts ADD CONSTRAINT quiz_attempts_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
```

**RLS Policies**:
```sql
-- Users can read own quiz attempts
CREATE POLICY "Users can read own quiz attempts"
  ON quiz_attempts FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert own quiz attempts
CREATE POLICY "Users can insert own quiz attempts"
  ON quiz_attempts FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);
```

---

### 8. User Badges Table
**Purpose**: Gamification and achievement system

```sql
CREATE TABLE user_badges (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  badge_name TEXT NOT NULL,
  badge_description TEXT NOT NULL,
  badge_icon TEXT NOT NULL,
  badge_category TEXT NOT NULL CHECK (badge_category IN ('progress', 'quiz', 'streak', 'time')),
  earned_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Columns**:
- `id` (UUID, PK): Unique badge identifier
- `user_id` (UUID, FK, NOT NULL): Badge recipient
- `badge_name` (TEXT, NOT NULL): Badge name/title
- `badge_description` (TEXT, NOT NULL): Badge description
- `badge_icon` (TEXT, NOT NULL): Icon identifier
- `badge_category` (TEXT, NOT NULL): Category (progress, quiz, streak, time)
- `earned_at` (TIMESTAMPTZ): Badge earning timestamp
- `created_at` (TIMESTAMPTZ): Record creation timestamp

**Indexes**:
```sql
CREATE INDEX idx_user_badges_user_id ON user_badges(user_id);
```

**Constraints**:
```sql
-- Category constraint
ALTER TABLE user_badges ADD CONSTRAINT user_badges_badge_category_check 
  CHECK (badge_category = ANY (ARRAY['progress'::text, 'quiz'::text, 'streak'::text, 'time'::text]));

-- Foreign key to users
ALTER TABLE user_badges ADD CONSTRAINT user_badges_user_id_fkey 
  FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
```

**RLS Policies**:
```sql
-- Users can read own badges
CREATE POLICY "Users can read own badges"
  ON user_badges FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- System can insert badges
CREATE POLICY "System can insert badges"
  ON user_badges FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);
```

---

## Database Functions & Triggers

### 1. User Profile Creation Function
**Purpose**: Automatically create user profile when new user registers

```sql
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profiles (id, email, name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1))
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user profile creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

### 2. Updated At Timestamp Function
**Purpose**: Automatically update updated_at column on record changes

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at columns
CREATE TRIGGER update_profiles_updated_at 
  BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_videos_updated_at 
  BEFORE UPDATE ON videos
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_playlists_updated_at 
  BEFORE UPDATE ON playlists
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_progress_updated_at 
  BEFORE UPDATE ON video_progress
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

---

## Relationships & Foreign Keys

### Entity Relationship Diagram
```
auth.users (Supabase Auth)
    ↓ (1:1)
profiles
    ↓ (1:many)
videos ←→ playlists (many:1, optional)
    ↓ (1:1)
video_progress
    ↓ (1:1, optional)
ai_summaries
    ↓ (1:1, optional)
quizzes
    ↓ (1:many)
quiz_attempts

auth.users
    ↓ (1:many)
user_badges
```

### Key Relationships
1. **Users → Profiles**: 1:1 relationship, cascade delete
2. **Users → Videos**: 1:many relationship, cascade delete
3. **Users → Playlists**: 1:many relationship, cascade delete
4. **Videos → Playlists**: many:1 relationship, set null on delete
5. **Videos → Progress**: 1:1 relationship, cascade delete
6. **Videos → Summaries**: 1:1 relationship, cascade delete
7. **Videos → Quizzes**: 1:1 relationship, cascade delete
8. **Quizzes → Attempts**: 1:many relationship, cascade delete
9. **Users → Badges**: 1:many relationship, cascade delete

---

## Data Types & Constraints

### Standard Data Types
- **UUID**: Primary keys and foreign keys
- **TEXT**: String data with no length limit
- **INTEGER**: Numeric data (points, duration, percentages)
- **TIMESTAMPTZ**: Timezone-aware timestamps
- **JSONB**: Structured data (preferences, questions, answers)
- **TEXT[]**: Arrays of strings (tags, key points)

### Custom Constraints
- **Status Enums**: CHECK constraints for status values
- **Percentage Ranges**: CHECK constraints for 0-100 ranges
- **Category Enums**: CHECK constraints for badge categories
- **Unique Combinations**: Composite unique constraints

### Default Values
- **UUIDs**: Auto-generated with uuid_generate_v4()
- **Timestamps**: NOW() for creation and update times
- **Arrays**: Empty arrays '{}' for TEXT[] columns
- **JSONB**: Default objects for structured data
- **Integers**: 0 for counters and percentages

---

## Performance Considerations

### Indexing Strategy
- **Primary Keys**: Automatic B-tree indexes
- **Foreign Keys**: Indexes on all foreign key columns
- **Query Patterns**: Indexes based on common query patterns
- **Composite Indexes**: For multi-column queries

### Query Optimization
- **Selective Queries**: Only fetch required columns
- **Join Optimization**: Efficient table joins
- **Pagination**: LIMIT/OFFSET for large result sets
- **Aggregation**: Efficient counting and grouping

### Scaling Considerations
- **Partitioning**: Future consideration for large tables
- **Read Replicas**: For read-heavy workloads
- **Connection Pooling**: Managed by Supabase
- **Caching**: Application-level caching strategies

---

## Security & Compliance

### Row Level Security (RLS)
- **Enabled**: All tables have RLS enabled
- **User Isolation**: Users can only access their own data
- **Policy Testing**: Comprehensive policy validation
- **Performance Impact**: Optimized policies for performance

### Data Privacy
- **GDPR Compliance**: User data rights and deletion
- **Data Minimization**: Only collect necessary data
- **Encryption**: Data encrypted at rest and in transit
- **Audit Trail**: Comprehensive logging and monitoring

### Access Control
- **Authentication Required**: All operations require valid auth
- **Role-Based Access**: Future consideration for admin roles
- **API Security**: Secure API key management
- **Input Validation**: Comprehensive data validation

---

## Migration Management

### Migration Files
Location: `/supabase/migrations/`
Format: `YYYYMMDDHHMMSS_description.sql`

### Migration Best Practices
- **Atomic Changes**: Each migration is a single transaction
- **Rollback Procedures**: Include rollback instructions
- **Testing**: Test migrations on staging environment
- **Documentation**: Comprehensive migration documentation

### Schema Versioning
- **Version Control**: All schema changes in git
- **Change Log**: Detailed change documentation
- **Backward Compatibility**: Maintain compatibility when possible
- **Breaking Changes**: Clear documentation and migration paths

---

**Last Updated**: December 2024  
**Schema Version**: 1.0  
**Next Review**: January 2025