# AmILearning - Adaptive Learning Platform

A modern learning platform that analyzes your educational journey and adapts content to provide personalized learning experiences.

## Features

- **Adaptive Learning Path**: Intelligent analysis that adapts to your learning journey
- **Personalized Content**: Summaries and quizzes tailored to your skill level
- **Progress Tracking**: Visual analytics and achievement system
- **Focus Mode**: Distraction-free learning environment
- **Multi-Platform Support**: Works with YouTube, Vimeo, and other video platforms

## Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Copy `.env.example` to `.env` and configure your API keys
4. Start the development server: `npm run dev`

## Environment Variables

Copy `.env.example` to `.env` and configure the following:

- `VITE_OPENAI_API_KEY`: Your OpenAI API key for content analysis
- `VITE_OPENAI_MODEL`: OpenAI model to use (default: gpt-4)
- `VITE_OPENAI_MAX_TOKENS`: Maximum tokens for API calls (default: 2000)

## Technology Stack

- React 18 with TypeScript
- Tailwind CSS for styling
- Lucide React for icons
- OpenAI API for content analysis
- Vite for development and building

## Contributing

This is a free platform that relies on community support. If you find it valuable, please consider contributing to help maintain and improve the service.
