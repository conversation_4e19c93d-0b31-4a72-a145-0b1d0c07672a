# OpenAI Configuration
VITE_OPENAI_API_KEY=your_openai_api_key_here
VITE_OPENAI_MODEL=gpt-3.5-turbo
VITE_OPENAI_MAX_TOKENS=500

# Perplexity Configuration
VITE_PERPLEXITY_API_KEY=your_perplexity_api_key_here
VITE_PERPLEXITY_MODEL=llama-3.1-sonar-small-128k-online

# Supabase Configuration (if using)
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# App Configuration
VITE_APP_NAME=AmILearning
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# Analytics (optional)
VITE_ANALYTICS_ID=your_analytics_id_here

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_OFFLINE_MODE=false

# Cost Optimization Notes:
# - GPT-3.5-turbo: ~$0.002/1K tokens (RECOMMENDED - cost-effective)
# - GPT-4: ~$0.03/1K tokens (15x more expensive)
# - 500 tokens ≈ 375 words (optimal balance for summaries and quizzes)
# - 1000 tokens ≈ 750 words (for detailed content)
# 
# Estimated costs per video with GPT-3.5-turbo:
# - Summary generation: ~$0.001-0.002 per video
# - Quiz generation: ~$0.001-0.002 per video
# - Total per video: ~$0.002-0.004 (very affordable!)