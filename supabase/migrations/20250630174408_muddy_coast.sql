/*
  # Update user preferences and improve error handling

  1. Changes
    - Add hasCompletedOnboarding to user preferences
    - Improve handle_new_user function with better error handling
    - Update existing profiles with new preference if needed

  2. Security
    - Maintain existing RLS policies
    - Improve trigger function error handling
*/

-- Update existing profiles to include hasCompletedOnboarding if not present
UPDATE profiles 
SET preferences = preferences || '{"hasCompletedOnboarding": false}'::jsonb
WHERE NOT (preferences ? 'hasCompletedOnboarding');

-- Update the default preferences for new profiles
ALTER TABLE profiles 
ALTER COLUMN preferences SET DEFAULT '{
  "darkMode": false,
  "focusMode": false,
  "autoplay": true,
  "notifications": true,
  "openaiModel": "gpt-3.5-turbo",
  "openaiMaxTokens": 500,
  "hasCompletedOnboarding": false
}'::jsonb;

-- <PERSON>reate improved function to handle user profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, name, preferences)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    COALESCE(
      NEW.raw_user_meta_data->'preferences',
      '{
        "darkMode": false,
        "focusMode": false,
        "autoplay": true,
        "notifications": true,
        "openaiModel": "gpt-3.5-turbo",
        "openaiMaxTokens": 500,
        "hasCompletedOnboarding": false
      }'::jsonb
    )
  );
  RETURN NEW;
EXCEPTION
  WHEN others THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate trigger for new user profile creation (drop first to avoid conflicts)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();