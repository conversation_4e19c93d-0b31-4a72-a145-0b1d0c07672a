// Environment validation utility
interface EnvConfig {
  name: string;
  required: boolean;
  validator?: (value: string) => boolean;
  description: string;
}

const ENV_CONFIGS: EnvConfig[] = [
  {
    name: 'VITE_SUPABASE_URL',
    required: true,
    validator: (value) => value.startsWith('https://') && value.includes('.supabase.co'),
    description: 'Supabase project URL'
  },
  {
    name: 'VITE_SUPABASE_ANON_KEY',
    required: true,
    validator: (value) => value.length > 100 && value.startsWith('eyJ'),
    description: 'Supabase anonymous key'
  },
  {
    name: 'VITE_OPENAI_API_KEY',
    required: false,
    validator: (value) => value.startsWith('sk-'),
    description: 'OpenAI API key'
  },
  {
    name: 'VITE_OPENAI_MODEL',
    required: false,
    validator: (value) => ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'].includes(value),
    description: 'OpenAI model name'
  },
  {
    name: 'VITE_OPENAI_MAX_TOKENS',
    required: false,
    validator: (value) => !isNaN(Number(value)) && Number(value) > 0 && Number(value) <= 4000,
    description: 'OpenAI max tokens'
  },
  {
    name: 'VITE_PERPLEXITY_API_KEY',
    required: false,
    validator: (value) => value.startsWith('pplx-'),
    description: 'Perplexity API key'
  },
  {
    name: 'VITE_APP_ENV',
    required: false,
    validator: (value) => ['development', 'staging', 'production'].includes(value),
    description: 'Application environment'
  }
];

interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  config: Record<string, string>;
}

export function validateEnvironment(): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const config: Record<string, string> = {};

  // Check each environment variable
  for (const envConfig of ENV_CONFIGS) {
    const value = import.meta.env[envConfig.name];
    
    if (!value) {
      if (envConfig.required) {
        errors.push(`Missing required environment variable: ${envConfig.name} (${envConfig.description})`);
      } else {
        warnings.push(`Optional environment variable not set: ${envConfig.name} (${envConfig.description})`);
      }
      continue;
    }

    // Validate the value if validator is provided
    if (envConfig.validator && !envConfig.validator(value)) {
      errors.push(`Invalid value for ${envConfig.name}: ${envConfig.description}`);
      continue;
    }

    config[envConfig.name] = value;
  }

  // Additional security checks
  if (import.meta.env.MODE === 'production') {
    // Production-specific validations
    if (!config.VITE_SUPABASE_URL?.includes('supabase.co')) {
      errors.push('Production builds must use official Supabase URLs');
    }

    // Check for development keys in production
    if (config.VITE_SUPABASE_URL?.includes('localhost') || config.VITE_SUPABASE_URL?.includes('127.0.0.1')) {
      errors.push('Cannot use localhost URLs in production');
    }
  }

  // Check for common security issues
  if (config.VITE_OPENAI_API_KEY && config.VITE_OPENAI_API_KEY.length < 20) {
    warnings.push('OpenAI API key appears to be too short');
  }

  if (config.VITE_SUPABASE_ANON_KEY && !config.VITE_SUPABASE_ANON_KEY.startsWith('eyJ')) {
    errors.push('Supabase anonymous key format appears invalid');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    config
  };
}

export function getSecureConfig() {
  const validation = validateEnvironment();
  
  if (!validation.valid) {
    console.error('Environment validation failed:', validation.errors);
    throw new Error(`Environment configuration errors: ${validation.errors.join(', ')}`);
  }

  if (validation.warnings.length > 0) {
    console.warn('Environment warnings:', validation.warnings);
  }

  return {
    supabase: {
      url: validation.config.VITE_SUPABASE_URL,
      anonKey: validation.config.VITE_SUPABASE_ANON_KEY
    },
    openai: {
      apiKey: validation.config.VITE_OPENAI_API_KEY,
      model: validation.config.VITE_OPENAI_MODEL || 'gpt-3.5-turbo',
      maxTokens: Number(validation.config.VITE_OPENAI_MAX_TOKENS) || 500
    },
    perplexity: {
      apiKey: validation.config.VITE_PERPLEXITY_API_KEY,
      model: validation.config.VITE_PERPLEXITY_MODEL || 'llama-3.1-sonar-small-128k-online'
    },
    app: {
      env: validation.config.VITE_APP_ENV || 'development',
      name: validation.config.VITE_APP_NAME || 'AmILearning',
      version: validation.config.VITE_APP_VERSION || '1.0.0'
    }
  };
}

// Runtime security checks
export function performSecurityChecks(): { passed: boolean; issues: string[] } {
  const issues: string[] = [];

  // Check if running in secure context (HTTPS in production)
  if (import.meta.env.MODE === 'production' && !window.isSecureContext) {
    issues.push('Application must be served over HTTPS in production');
  }

  // Check for common security headers
  const requiredHeaders = [
    'X-Frame-Options',
    'X-Content-Type-Options',
    'X-XSS-Protection'
  ];

  // Note: We can't directly check response headers from client-side,
  // but we can check if certain security features are available
  
  // Check if Content Security Policy is active
  // Note: We avoid using eval() for security reasons
  // Instead, we check for other CSP indicators
  if (import.meta.env.MODE === 'production') {
    // Check if we're in a secure context
    if (!window.isSecureContext) {
      issues.push('Application should run in a secure context (HTTPS)');
    }
  }

  // Check for localStorage availability (should be available in secure contexts)
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
  } catch (e) {
    issues.push('localStorage not available - may indicate security restrictions');
  }

  // Check for sessionStorage availability
  try {
    sessionStorage.setItem('test', 'test');
    sessionStorage.removeItem('test');
  } catch (e) {
    issues.push('sessionStorage not available - may indicate security restrictions');
  }

  return {
    passed: issues.length === 0,
    issues
  };
}

// Initialize environment validation on module load
let envValidated = false;

export function initializeEnvironment() {
  if (envValidated) return;

  try {
    const config = getSecureConfig();
    console.log('Environment validation passed');
    
    // Perform runtime security checks
    const securityCheck = performSecurityChecks();
    if (!securityCheck.passed) {
      console.warn('Security check issues:', securityCheck.issues);
    }
    
    envValidated = true;
    return config;
  } catch (error) {
    console.error('Environment initialization failed:', error);
    throw error;
  }
}

// Auto-initialize in development
if (import.meta.env.MODE === 'development') {
  try {
    initializeEnvironment();
  } catch (error) {
    console.error('Development environment validation failed:', error);
  }
}
