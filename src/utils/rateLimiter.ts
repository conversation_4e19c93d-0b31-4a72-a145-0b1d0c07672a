// Rate limiting utility for API protection
interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  blockDurationMs?: number;
}

interface RateLimitEntry {
  requests: number[];
  blockedUntil?: number;
}

class RateLimiter {
  private limits: Map<string, RateLimitEntry> = new Map();
  private configs: Map<string, RateLimitConfig> = new Map();

  constructor() {
    // Clean up old entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  // Configure rate limits for different operations
  configure(operation: string, config: RateLimitConfig) {
    this.configs.set(operation, config);
  }

  // Check if request is allowed
  isAllowed(identifier: string, operation: string): { allowed: boolean; retryAfter?: number } {
    const config = this.configs.get(operation);
    if (!config) {
      // No rate limit configured, allow request
      return { allowed: true };
    }

    const key = `${identifier}-${operation}`;
    const now = Date.now();
    const entry = this.limits.get(key) || { requests: [] };

    // Check if currently blocked
    if (entry.blockedUntil && now < entry.blockedUntil) {
      return { 
        allowed: false, 
        retryAfter: Math.ceil((entry.blockedUntil - now) / 1000) 
      };
    }

    // Remove old requests outside the window
    entry.requests = entry.requests.filter(time => now - time < config.windowMs);

    // Check if limit exceeded
    if (entry.requests.length >= config.maxRequests) {
      // Block for additional time if configured
      if (config.blockDurationMs) {
        entry.blockedUntil = now + config.blockDurationMs;
      }
      
      this.limits.set(key, entry);
      return { 
        allowed: false, 
        retryAfter: Math.ceil(config.windowMs / 1000) 
      };
    }

    // Allow request and record it
    entry.requests.push(now);
    entry.blockedUntil = undefined; // Clear any previous block
    this.limits.set(key, entry);
    
    return { allowed: true };
  }

  // Record a request (for tracking purposes)
  recordRequest(identifier: string, operation: string) {
    const key = `${identifier}-${operation}`;
    const now = Date.now();
    const entry = this.limits.get(key) || { requests: [] };
    
    entry.requests.push(now);
    this.limits.set(key, entry);
  }

  // Get current usage for an identifier/operation
  getUsage(identifier: string, operation: string): { current: number; limit: number; resetTime: number } {
    const config = this.configs.get(operation);
    if (!config) {
      return { current: 0, limit: Infinity, resetTime: 0 };
    }

    const key = `${identifier}-${operation}`;
    const now = Date.now();
    const entry = this.limits.get(key) || { requests: [] };
    
    // Filter to current window
    const currentRequests = entry.requests.filter(time => now - time < config.windowMs);
    const oldestRequest = Math.min(...currentRequests);
    const resetTime = oldestRequest + config.windowMs;

    return {
      current: currentRequests.length,
      limit: config.maxRequests,
      resetTime: resetTime
    };
  }

  // Clean up old entries
  private cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [key, entry] of this.limits.entries()) {
      // Remove entries with no recent requests and not blocked
      const hasRecentRequests = entry.requests.some(time => now - time < maxAge);
      const isBlocked = entry.blockedUntil && now < entry.blockedUntil;
      
      if (!hasRecentRequests && !isBlocked) {
        this.limits.delete(key);
      }
    }
  }

  // Reset limits for a specific identifier (admin function)
  reset(identifier: string, operation?: string) {
    if (operation) {
      const key = `${identifier}-${operation}`;
      this.limits.delete(key);
    } else {
      // Reset all operations for this identifier
      for (const key of this.limits.keys()) {
        if (key.startsWith(`${identifier}-`)) {
          this.limits.delete(key);
        }
      }
    }
  }
}

// Global rate limiter instance
export const rateLimiter = new RateLimiter();

// Configure default rate limits
rateLimiter.configure('auth-signup', {
  maxRequests: 3,
  windowMs: 15 * 60 * 1000, // 15 minutes
  blockDurationMs: 30 * 60 * 1000 // Block for 30 minutes after limit
});

rateLimiter.configure('auth-signin', {
  maxRequests: 5,
  windowMs: 15 * 60 * 1000, // 15 minutes
  blockDurationMs: 15 * 60 * 1000 // Block for 15 minutes after limit
});

rateLimiter.configure('api-request', {
  maxRequests: 100,
  windowMs: 60 * 1000, // 1 minute
  blockDurationMs: 5 * 60 * 1000 // Block for 5 minutes after limit
});

rateLimiter.configure('password-reset', {
  maxRequests: 3,
  windowMs: 60 * 60 * 1000, // 1 hour
  blockDurationMs: 60 * 60 * 1000 // Block for 1 hour after limit
});

// Utility functions
export function getClientIdentifier(): string {
  // Use a combination of factors to identify the client
  const userAgent = navigator.userAgent;
  const language = navigator.language;
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  
  // Create a simple hash of these factors
  const identifier = btoa(`${userAgent}-${language}-${timezone}`).slice(0, 16);
  return identifier;
}

export function checkRateLimit(operation: string, userId?: string): { allowed: boolean; retryAfter?: number } {
  const identifier = userId || getClientIdentifier();
  return rateLimiter.isAllowed(identifier, operation);
}

export function recordRequest(operation: string, userId?: string) {
  const identifier = userId || getClientIdentifier();
  rateLimiter.recordRequest(identifier, operation);
}

// Rate limit middleware for async functions
export function withRateLimit<T extends any[], R>(
  operation: string,
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const check = checkRateLimit(operation);
    
    if (!check.allowed) {
      const error = new Error(`Rate limit exceeded for ${operation}. Try again in ${check.retryAfter} seconds.`);
      (error as any).retryAfter = check.retryAfter;
      throw error;
    }
    
    recordRequest(operation);
    return fn(...args);
  };
}
