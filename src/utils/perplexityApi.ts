import { LearningPath, RecommendedVideo, LearningCategory } from '../types';

// Perplexity API configuration
const PERPLEXITY_API_URL = 'https://api.perplexity.ai/chat/completions';

interface PerplexityConfig {
  apiKey: string;
  model: string;
}

function getPerplexityConfig(): PerplexityConfig {
  // Try to get from user preferences first, then environment
  try {
    const savedUser = localStorage.getItem('amiLearning_user');
    if (savedUser) {
      const user = JSON.parse(savedUser);
      if (user.preferences?.perplexityApiKey) {
        return {
          apiKey: user.preferences.perplexityApiKey,
          model: user.preferences.perplexityModel || 'llama-3.1-sonar-small-128k-online'
        };
      }
    }
  } catch (error) {
    console.warn('Error reading user preferences:', error);
  }
  
  // Fallback to environment variables
  return {
    apiKey: import.meta.env.VITE_PERPLEXITY_API_KEY || '',
    model: import.meta.env.VITE_PERPLEXITY_MODEL || 'llama-3.1-sonar-small-128k-online'
  };
}

export async function generateLearningPath(
  category: string,
  difficulty: 'beginner' | 'intermediate' | 'advanced',
  timeCommitment: number,
  specificGoals?: string[]
): Promise<LearningPath> {
  const config = getPerplexityConfig();
  
  if (!config.apiKey) {
    console.warn('Perplexity API key not configured. Returning mock learning path.');
    return createMockLearningPath(category, difficulty);
  }

  try {
    const prompt = createLearningPathPrompt(category, difficulty, timeCommitment, specificGoals);
    
    const response = await fetch(PERPLEXITY_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert learning path curator. Research and recommend the best YouTube videos for structured learning paths. Return responses in valid JSON format only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`Perplexity API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;
    
    if (!content) {
      throw new Error('No content received from Perplexity API');
    }

    // Parse the JSON response
    const pathData = JSON.parse(content);
    return transformPerplexityResponse(pathData, category, difficulty);

  } catch (error) {
    console.error('Error generating learning path:', error);
    return createMockLearningPath(category, difficulty);
  }
}

function createLearningPathPrompt(
  category: string,
  difficulty: string,
  timeCommitment: number,
  specificGoals?: string[]
): string {
  const goalsText = specificGoals?.length ? ` with focus on: ${specificGoals.join(', ')}` : '';
  
  return `Research and create a comprehensive learning path for "${category}" at ${difficulty} level${goalsText}.

Requirements:
- Find 8-12 high-quality YouTube videos that form a logical progression
- Include video titles, URLs, descriptions, and estimated durations
- Ensure videos are from reputable channels with good ratings
- Total path should take approximately ${timeCommitment} hours per week for 4-6 weeks
- Include prerequisite knowledge and learning objectives
- Arrange videos in optimal learning order

Return ONLY valid JSON in this exact format:
{
  "title": "Learning Path Title",
  "description": "Comprehensive description of what learners will achieve",
  "estimatedDuration": 24,
  "prerequisites": ["prerequisite 1", "prerequisite 2"],
  "learningObjectives": ["objective 1", "objective 2", "objective 3"],
  "videos": [
    {
      "title": "Video Title",
      "url": "https://youtube.com/watch?v=...",
      "description": "What this video covers",
      "duration": 1800,
      "difficulty": "beginner",
      "topics": ["topic1", "topic2"],
      "order": 1,
      "isRequired": true
    }
  ],
  "tags": ["tag1", "tag2", "tag3"]
}`;
}

function transformPerplexityResponse(
  data: any,
  category: string,
  difficulty: 'beginner' | 'intermediate' | 'advanced'
): LearningPath {
  const videos: RecommendedVideo[] = data.videos?.map((video: any, index: number) => ({
    id: crypto.randomUUID(),
    title: video.title || `Video ${index + 1}`,
    url: video.url || '',
    description: video.description || '',
    thumbnail: extractYouTubeThumbnail(video.url) || '',
    duration: video.duration || 1800,
    difficulty: video.difficulty || difficulty,
    topics: video.topics || [],
    order: video.order || index + 1,
    isRequired: video.isRequired !== false
  })) || [];

  return {
    id: crypto.randomUUID(),
    title: data.title || `${category} Learning Path`,
    description: data.description || `Comprehensive ${category} learning journey`,
    category,
    difficulty,
    estimatedDuration: data.estimatedDuration || 20,
    videos,
    prerequisites: data.prerequisites || [],
    learningObjectives: data.learningObjectives || [],
    tags: data.tags || [category.toLowerCase()],
    createdAt: new Date(),
    isPopular: false,
    enrollmentCount: 0
  };
}

function extractYouTubeThumbnail(url: string): string {
  const videoId = extractVideoId(url);
  if (videoId && url.includes('youtube')) {
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  }
  return 'https://images.pexels.com/photos/5428836/pexels-photo-5428836.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1';
}

function extractVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /(?:vimeo\.com\/)([0-9]+)/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) return match[1];
  }
  return null;
}

function createMockLearningPath(
  category: string,
  difficulty: 'beginner' | 'intermediate' | 'advanced'
): LearningPath {
  const mockVideos: RecommendedVideo[] = [
    {
      id: crypto.randomUUID(),
      title: `${category} Fundamentals - Getting Started`,
      url: 'https://youtube.com/watch?v=mock1',
      description: `Introduction to ${category} concepts and basics`,
      thumbnail: 'https://images.pexels.com/photos/5428836/pexels-photo-5428836.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1',
      duration: 1800,
      difficulty: 'beginner',
      topics: ['fundamentals', 'introduction'],
      order: 1,
      isRequired: true
    },
    {
      id: crypto.randomUUID(),
      title: `${category} Core Concepts`,
      url: 'https://youtube.com/watch?v=mock2',
      description: `Deep dive into core ${category} principles`,
      thumbnail: 'https://images.pexels.com/photos/574071/pexels-photo-574071.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1',
      duration: 2400,
      difficulty,
      topics: ['core concepts', 'principles'],
      order: 2,
      isRequired: true
    }
  ];

  return {
    id: crypto.randomUUID(),
    title: `${category} Learning Path`,
    description: `Comprehensive ${category} learning journey from ${difficulty} to advanced level. Note: Perplexity API key not configured - this is sample content.`,
    category,
    difficulty,
    estimatedDuration: 20,
    videos: mockVideos,
    prerequisites: difficulty === 'beginner' ? [] : [`Basic ${category} knowledge`],
    learningObjectives: [
      `Understand ${category} fundamentals`,
      `Apply ${category} concepts in practice`,
      `Build confidence in ${category} skills`
    ],
    tags: [category.toLowerCase(), difficulty],
    createdAt: new Date(),
    isPopular: true,
    enrollmentCount: 0
  };
}

export async function getPopularLearningCategories(): Promise<LearningCategory[]> {
  // This could also use Perplexity API to get trending topics
  return [
    {
      id: 'software-development',
      name: 'Software Development',
      description: 'Programming languages, frameworks, and development practices',
      icon: 'code',
      subcategories: ['JavaScript', 'Python', 'React', 'Node.js', 'TypeScript'],
      popularPaths: ['Full Stack Development', 'Frontend Development', 'Backend Development'],
      skillLevels: ['beginner', 'intermediate', 'advanced']
    },
    {
      id: 'data-science',
      name: 'Data Science',
      description: 'Data analysis, machine learning, and statistical modeling',
      icon: 'bar-chart',
      subcategories: ['Python', 'R', 'Machine Learning', 'Statistics', 'SQL'],
      popularPaths: ['Data Analysis', 'Machine Learning', 'Data Visualization'],
      skillLevels: ['beginner', 'intermediate', 'advanced']
    },
    {
      id: 'digital-marketing',
      name: 'Digital Marketing',
      description: 'Online marketing strategies and tools',
      icon: 'trending-up',
      subcategories: ['SEO', 'Social Media', 'Content Marketing', 'PPC', 'Analytics'],
      popularPaths: ['SEO Mastery', 'Social Media Marketing', 'Content Strategy'],
      skillLevels: ['beginner', 'intermediate', 'advanced']
    },
    {
      id: 'design',
      name: 'Design',
      description: 'UI/UX design, graphic design, and design thinking',
      icon: 'palette',
      subcategories: ['UI/UX', 'Graphic Design', 'Figma', 'Adobe Creative Suite'],
      popularPaths: ['UI/UX Design', 'Graphic Design', 'Design Systems'],
      skillLevels: ['beginner', 'intermediate', 'advanced']
    },
    {
      id: 'business',
      name: 'Business & Entrepreneurship',
      description: 'Business strategy, entrepreneurship, and management',
      icon: 'briefcase',
      subcategories: ['Strategy', 'Leadership', 'Finance', 'Marketing', 'Operations'],
      popularPaths: ['Business Strategy', 'Leadership Skills', 'Startup Fundamentals'],
      skillLevels: ['beginner', 'intermediate', 'advanced']
    },
    {
      id: 'productivity',
      name: 'Productivity & Personal Development',
      description: 'Time management, productivity tools, and personal growth',
      icon: 'clock',
      subcategories: ['Time Management', 'Goal Setting', 'Habits', 'Tools', 'Mindset'],
      popularPaths: ['Productivity Mastery', 'Goal Achievement', 'Habit Formation'],
      skillLevels: ['beginner', 'intermediate', 'advanced']
    }
  ];
}

export async function searchLearningPaths(
  query: string,
  category?: string,
  difficulty?: string
): Promise<LearningPath[]> {
  // This would use Perplexity API to search for specific learning paths
  // For now, return mock results
  const categories = await getPopularLearningCategories();
  const matchingCategory = categories.find(cat => 
    cat.name.toLowerCase().includes(query.toLowerCase()) ||
    cat.subcategories.some(sub => sub.toLowerCase().includes(query.toLowerCase()))
  );

  if (matchingCategory) {
    return [await generateLearningPath(
      matchingCategory.name,
      difficulty as any || 'beginner',
      10
    )];
  }

  return [];
}