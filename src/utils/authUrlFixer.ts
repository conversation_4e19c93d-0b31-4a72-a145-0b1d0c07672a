// Utility to handle malformed Supabase auth URLs and redirect properly

interface AuthTokens {
  access_token?: string;
  refresh_token?: string;
  expires_at?: string;
  expires_in?: string;
  token_type?: string;
  type?: string;
  error?: string;
  error_description?: string;
}

export function extractAuthTokensFromUrl(url: string = window.location.href): AuthTokens {
  try {
    // Handle both hash and search parameters
    const urlObj = new URL(url);
    const hashParams = new URLSearchParams(urlObj.hash.substring(1));
    const searchParams = new URLSearchParams(urlObj.search);
    
    return {
      access_token: hashParams.get('access_token') || searchParams.get('access_token') || undefined,
      refresh_token: hashParams.get('refresh_token') || searchParams.get('refresh_token') || undefined,
      expires_at: hashParams.get('expires_at') || searchParams.get('expires_at') || undefined,
      expires_in: hashParams.get('expires_in') || searchParams.get('expires_in') || undefined,
      token_type: hashParams.get('token_type') || searchParams.get('token_type') || undefined,
      type: hashParams.get('type') || searchParams.get('type') || undefined,
      error: hashParams.get('error') || searchParams.get('error') || undefined,
      error_description: hashParams.get('error_description') || searchParams.get('error_description') || undefined
    };
  } catch (error) {
    console.error('Error extracting auth tokens:', error);
    return {};
  }
}

export function isSupabaseAuthUrl(url: string = window.location.href): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.includes('supabase.co') && 
           (url.includes('access_token') || url.includes('error'));
  } catch {
    return false;
  }
}

export function buildCorrectAuthUrl(tokens: AuthTokens, targetDomain?: string): string {
  // Determine target domain
  let domain = targetDomain;
  if (!domain) {
    if (window.location.hostname === 'localhost') {
      domain = 'http://localhost:5173';
    } else if (window.location.hostname.includes('vercel.app')) {
      domain = 'https://amilearning-bolt.vercel.app';
    } else {
      domain = window.location.origin;
    }
  }
  
  // Build URL with tokens
  const params = new URLSearchParams();
  Object.entries(tokens).forEach(([key, value]) => {
    if (value) {
      params.set(key, value);
    }
  });
  
  return `${domain}/auth/callback?${params.toString()}`;
}

export function handleMalformedAuthUrl(): boolean {
  const currentUrl = window.location.href;
  
  // Check if this is a malformed Supabase auth URL
  if (isSupabaseAuthUrl(currentUrl)) {
    console.log('Detected malformed Supabase auth URL, attempting to fix...');
    
    const tokens = extractAuthTokensFromUrl(currentUrl);
    
    if (tokens.access_token || tokens.error) {
      const correctUrl = buildCorrectAuthUrl(tokens);
      console.log('Redirecting to correct URL:', correctUrl);
      
      // Show a brief message before redirecting
      document.body.innerHTML = `
        <div style="
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          margin: 0;
          padding: 20px;
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div style="
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 400px;
          ">
            <h2 style="color: #333; margin-bottom: 20px;">Email Confirmed!</h2>
            <p style="color: #666; margin-bottom: 20px;">Redirecting you to the application...</p>
            <div style="
              border: 3px solid #f3f3f3;
              border-top: 3px solid #667eea;
              border-radius: 50%;
              width: 40px;
              height: 40px;
              animation: spin 1s linear infinite;
              margin: 20px auto;
            "></div>
          </div>
        </div>
        <style>
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
      `;
      
      // Redirect after a short delay
      setTimeout(() => {
        window.location.href = correctUrl;
      }, 2000);
      
      return true; // Handled
    }
  }
  
  return false; // Not handled
}

// Auto-fix malformed URLs when this module is imported
if (typeof window !== 'undefined') {
  // Only run on page load, not on every import
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      handleMalformedAuthUrl();
    });
  } else {
    handleMalformedAuthUrl();
  }
}

export default {
  extractAuthTokensFromUrl,
  isSupabaseAuthUrl,
  buildCorrectAuthUrl,
  handleMalformedAuthUrl
};
