import { supabase } from '../lib/supabase';

// Simple encryption/decryption for client-side storage
// Note: This is basic obfuscation, not true encryption
// For production, consider using a proper encryption library
const ENCRYPTION_KEY = 'amilearning-secure-key-2024';

function simpleEncrypt(text: string): string {
  try {
    // Basic XOR encryption for client-side obfuscation
    let encrypted = '';
    for (let i = 0; i < text.length; i++) {
      const keyChar = ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length);
      const textChar = text.charCodeAt(i);
      encrypted += String.fromCharCode(textChar ^ keyChar);
    }
    return btoa(encrypted); // Base64 encode
  } catch (error) {
    console.error('Encryption error:', error);
    return text; // Return original if encryption fails
  }
}

function simpleDecrypt(encryptedText: string): string {
  try {
    const decoded = atob(encryptedText); // Base64 decode
    let decrypted = '';
    for (let i = 0; i < decoded.length; i++) {
      const keyChar = ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length);
      const encryptedChar = decoded.charCodeAt(i);
      decrypted += String.fromCharCode(encryptedChar ^ keyChar);
    }
    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    return encryptedText; // Return original if decryption fails
  }
}

// Rate limiting for API key operations
const rateLimiter = new Map<string, number[]>();

function checkRateLimit(userId: string, action: string, maxRequests = 5, windowMs = 60000): boolean {
  const key = `${userId}-${action}`;
  const now = Date.now();
  const requests = rateLimiter.get(key) || [];
  
  // Remove old requests outside the window
  const validRequests = requests.filter(time => now - time < windowMs);
  
  if (validRequests.length >= maxRequests) {
    return false; // Rate limit exceeded
  }
  
  validRequests.push(now);
  rateLimiter.set(key, validRequests);
  return true;
}

export const secureStorage = {
  // Store API key securely in user profile
  async storeApiKey(apiKey: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Check rate limit
      if (!checkRateLimit(user.id, 'store-api-key', 3, 300000)) { // 3 requests per 5 minutes
        return { success: false, error: 'Rate limit exceeded. Please try again later.' };
      }

      // Encrypt the API key
      const encryptedKey = simpleEncrypt(apiKey);
      
      // Update user profile with encrypted API key
      const { error } = await supabase
        .from('profiles')
        .update({ 
          preferences: {
            ...user.user_metadata.preferences,
            openaiApiKey: encryptedKey
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) {
        console.error('Error storing API key:', error);
        return { success: false, error: 'Failed to store API key securely' };
      }

      return { success: true };
    } catch (error: any) {
      console.error('Secure storage error:', error);
      return { success: false, error: error.message || 'An unexpected error occurred' };
    }
  },

  // Retrieve API key securely from user profile
  async getApiKey(): Promise<{ apiKey?: string; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { error: 'User not authenticated' };
      }

      // Check rate limit
      if (!checkRateLimit(user.id, 'get-api-key', 10, 60000)) { // 10 requests per minute
        return { error: 'Rate limit exceeded. Please try again later.' };
      }

      // Get user profile
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('preferences')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error retrieving profile:', error);
        return { error: 'Failed to retrieve user profile' };
      }

      const encryptedKey = profile?.preferences?.openaiApiKey;
      if (!encryptedKey) {
        return { apiKey: undefined }; // No API key stored
      }

      // Decrypt the API key
      const apiKey = simpleDecrypt(encryptedKey);
      return { apiKey };
    } catch (error: any) {
      console.error('Secure retrieval error:', error);
      return { error: error.message || 'An unexpected error occurred' };
    }
  },

  // Remove API key from storage
  async removeApiKey(): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Check rate limit
      if (!checkRateLimit(user.id, 'remove-api-key', 3, 300000)) { // 3 requests per 5 minutes
        return { success: false, error: 'Rate limit exceeded. Please try again later.' };
      }

      // Update user profile to remove API key
      const { error } = await supabase
        .from('profiles')
        .update({ 
          preferences: {
            ...user.user_metadata.preferences,
            openaiApiKey: null
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) {
        console.error('Error removing API key:', error);
        return { success: false, error: 'Failed to remove API key' };
      }

      return { success: true };
    } catch (error: any) {
      console.error('Secure removal error:', error);
      return { success: false, error: error.message || 'An unexpected error occurred' };
    }
  },

  // Validate API key format
  validateApiKey(apiKey: string): { valid: boolean; error?: string } {
    if (!apiKey || typeof apiKey !== 'string') {
      return { valid: false, error: 'API key is required' };
    }

    if (!apiKey.startsWith('sk-')) {
      return { valid: false, error: 'Invalid API key format. OpenAI keys start with "sk-"' };
    }

    if (apiKey.length < 20) {
      return { valid: false, error: 'API key appears to be too short' };
    }

    return { valid: true };
  }
};
