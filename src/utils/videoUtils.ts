export function extractVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /(?:vimeo\.com\/)([0-9]+)/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) return match[1];
  }
  return null;
}

export function getVideoThumbnail(url: string): string {
  const videoId = extractVideoId(url);
  if (!videoId) return '/api/placeholder/320/180';

  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  }
  
  // Default placeholder for other video platforms
  return '/api/placeholder/320/180';
}

export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export function calculateProgress(watchTime: number, duration: number): number {
  return Math.min(Math.round((watchTime / duration) * 100), 100);
}

export function getProgressStatus(completionPercentage: number): 'not_started' | 'in_progress' | 'completed' {
  if (completionPercentage === 0) return 'not_started';
  if (completionPercentage >= 90) return 'completed';
  return 'in_progress';
}