import { User, Video, Playlist, AISummary, Quiz } from '../types';

export function createDemoUser(email: string, name?: string): User {
  return {
    id: 'demo-user-123',
    email: email,
    name: name || 'Demo User',
    points: 1250,
    badges: [
      {
        id: 'first-video',
        name: 'First Steps',
        description: 'Added your first video',
        icon: 'play',
        earnedAt: new Date('2024-01-15'),
        category: 'progress'
      },
      {
        id: 'week-streak',
        name: 'Week Warrior',
        description: 'Maintained a 7-day learning streak',
        icon: 'target',
        earnedAt: new Date('2024-01-20'),
        category: 'streak'
      },
      {
        id: 'quiz-master',
        name: 'Quiz Master',
        description: 'Scored 90%+ on 5 quizzes',
        icon: 'brain',
        earnedAt: new Date('2024-01-25'),
        category: 'quiz'
      }
    ],
    preferences: {
      darkMode: false,
      focusMode: false,
      autoplay: true,
      notifications: true,
      openaiModel: 'gpt-3.5-turbo',
      openaiMaxTokens: 500,
    },
    createdAt: new Date(),
  };
}

export function getDemoVideos(): Video[] {
  const baseDate = new Date();
  
  return [
    {
      id: 'demo-video-1',
      url: 'https://youtube.com/watch?v=dQw4w9WgXcQ',
      title: 'Introduction to React Hooks',
      description: 'Learn the fundamentals of React hooks including useState, useEffect, and custom hooks.',
      thumbnail: 'https://images.pexels.com/photos/11035380/pexels-photo-11035380.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1',
      duration: 1800, // 30 minutes
      addedAt: new Date(baseDate.getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      tags: ['react', 'javascript', 'hooks', 'frontend'],
      playlist: 'demo-playlist-1',
      progress: {
        status: 'completed',
        watchTime: 1800,
        lastWatched: new Date(baseDate.getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        completionPercentage: 100,
      },
      summary: {
        id: 'summary-1',
        content: 'This comprehensive tutorial covers React hooks, starting with the basics of useState for managing component state, then diving into useEffect for handling side effects. The video demonstrates practical examples and best practices for using hooks effectively in modern React applications.',
        keyPoints: [
          'Understanding the useState hook for state management',
          'Using useEffect for side effects and lifecycle methods',
          'Creating custom hooks for reusable logic',
          'Best practices and common pitfalls to avoid'
        ],
        generatedAt: new Date(baseDate.getTime() - 7 * 24 * 60 * 60 * 1000),
      },
      quiz: {
        id: 'quiz-1',
        questions: [
          {
            id: 'q1-1',
            question: 'What is the primary purpose of the useState hook?',
            options: [
              'To handle side effects',
              'To manage component state',
              'To create custom hooks',
              'To optimize performance'
            ],
            correctAnswer: 1,
            explanation: 'useState is specifically designed for managing state in functional components.'
          },
          {
            id: 'q1-2',
            question: 'When does useEffect run by default?',
            options: [
              'Only on component mount',
              'Only on component unmount',
              'After every render',
              'Only when dependencies change'
            ],
            correctAnswer: 2,
            explanation: 'By default, useEffect runs after every render unless you provide a dependency array.'
          }
        ],
        userAnswers: [
          { questionId: 'q1-1', selectedAnswer: 1, isCorrect: true },
          { questionId: 'q1-2', selectedAnswer: 2, isCorrect: true }
        ],
        score: 100,
        completedAt: new Date(baseDate.getTime() - 2 * 24 * 60 * 60 * 1000),
      }
    },
    {
      id: 'demo-video-2',
      url: 'https://youtube.com/watch?v=example2',
      title: 'Advanced TypeScript Patterns',
      description: 'Explore advanced TypeScript features including generics, utility types, and conditional types.',
      thumbnail: 'https://images.pexels.com/photos/574071/pexels-photo-574071.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1',
      duration: 2400, // 40 minutes
      addedAt: new Date(baseDate.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      tags: ['typescript', 'javascript', 'programming', 'advanced'],
      playlist: 'demo-playlist-1',
      progress: {
        status: 'in_progress',
        watchTime: 1440, // 24 minutes watched
        lastWatched: new Date(baseDate.getTime() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        completionPercentage: 60,
      },
      summary: {
        id: 'summary-2',
        content: 'This advanced tutorial explores sophisticated TypeScript patterns that help build more robust and maintainable applications. Topics include generic constraints, mapped types, conditional types, and practical examples of how to leverage TypeScript\'s type system effectively.',
        keyPoints: [
          'Generic types and constraints for flexible APIs',
          'Utility types like Partial, Pick, and Omit',
          'Conditional types for advanced type manipulation',
          'Mapped types for transforming existing types'
        ],
        generatedAt: new Date(baseDate.getTime() - 5 * 24 * 60 * 60 * 1000),
      },
      quiz: {
        id: 'quiz-2',
        questions: [
          {
            id: 'q2-1',
            question: 'What are generic constraints used for in TypeScript?',
            options: [
              'To limit the types that can be used with generics',
              'To improve performance',
              'To add runtime validation',
              'To enable inheritance'
            ],
            correctAnswer: 0,
            explanation: 'Generic constraints allow you to restrict the types that can be used with a generic parameter.'
          }
        ]
      }
    },
    {
      id: 'demo-video-3',
      url: 'https://youtube.com/watch?v=example3',
      title: 'CSS Grid Layout Mastery',
      description: 'Master CSS Grid with practical examples and real-world layouts.',
      thumbnail: 'https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1',
      duration: 2100, // 35 minutes
      addedAt: new Date(baseDate.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      tags: ['css', 'grid', 'layout', 'design'],
      playlist: 'demo-playlist-2',
      progress: {
        status: 'not_started',
        watchTime: 0,
        lastWatched: new Date(baseDate.getTime() - 3 * 24 * 60 * 60 * 1000),
        completionPercentage: 0,
      },
      summary: {
        id: 'summary-3',
        content: 'A comprehensive guide to CSS Grid that covers everything from basic grid concepts to advanced layout techniques. Learn how to create responsive, flexible layouts that work across all devices and browsers.',
        keyPoints: [
          'Grid container and grid item fundamentals',
          'Grid template areas for semantic layouts',
          'Responsive grid patterns and techniques',
          'Grid vs Flexbox: when to use each'
        ],
        generatedAt: new Date(baseDate.getTime() - 3 * 24 * 60 * 60 * 1000),
      },
      quiz: {
        id: 'quiz-3',
        questions: [
          {
            id: 'q3-1',
            question: 'What is the difference between grid-template-columns and grid-auto-columns?',
            options: [
              'There is no difference',
              'grid-template-columns defines explicit tracks, grid-auto-columns defines implicit tracks',
              'grid-auto-columns is deprecated',
              'grid-template-columns is for rows only'
            ],
            correctAnswer: 1,
            explanation: 'grid-template-columns defines the size of explicitly created columns, while grid-auto-columns sets the size of implicitly created columns.'
          }
        ]
      }
    },
    {
      id: 'demo-video-4',
      url: 'https://youtube.com/watch?v=example4',
      title: 'Node.js Performance Optimization',
      description: 'Learn techniques to optimize Node.js applications for better performance and scalability.',
      thumbnail: 'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1',
      duration: 3000, // 50 minutes
      addedAt: new Date(baseDate.getTime() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      tags: ['nodejs', 'performance', 'backend', 'optimization'],
      progress: {
        status: 'in_progress',
        watchTime: 900, // 15 minutes watched
        lastWatched: new Date(baseDate.getTime() - 6 * 60 * 60 * 1000), // 6 hours ago
        completionPercentage: 30,
      },
      summary: {
        id: 'summary-4',
        content: 'This tutorial focuses on practical Node.js performance optimization techniques including memory management, event loop optimization, clustering, and profiling tools to identify bottlenecks.',
        keyPoints: [
          'Memory leak detection and prevention',
          'Event loop optimization strategies',
          'Clustering for multi-core utilization',
          'Profiling tools and performance monitoring'
        ],
        generatedAt: new Date(baseDate.getTime() - 1 * 24 * 60 * 60 * 1000),
      },
      quiz: {
        id: 'quiz-4',
        questions: [
          {
            id: 'q4-1',
            question: 'What is the primary benefit of using Node.js clustering?',
            options: [
              'Reduced memory usage',
              'Better error handling',
              'Utilizing multiple CPU cores',
              'Faster file I/O'
            ],
            correctAnswer: 2,
            explanation: 'Clustering allows Node.js to create child processes that share server ports, enabling utilization of multiple CPU cores.'
          }
        ]
      }
    },
    {
      id: 'demo-video-5',
      url: 'https://youtube.com/watch?v=example5',
      title: 'Modern JavaScript ES2024 Features',
      description: 'Explore the latest JavaScript features and how to use them in modern applications.',
      thumbnail: 'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1',
      duration: 1500, // 25 minutes
      addedAt: new Date(baseDate.getTime() - 12 * 60 * 60 * 1000), // 12 hours ago
      tags: ['javascript', 'es2024', 'modern', 'features'],
      playlist: 'demo-playlist-1',
      progress: {
        status: 'not_started',
        watchTime: 0,
        lastWatched: new Date(baseDate.getTime() - 12 * 60 * 60 * 1000),
        completionPercentage: 0,
      },
      summary: {
        id: 'summary-5',
        content: 'Stay up-to-date with the latest JavaScript features introduced in ES2024. This video covers new syntax, improved APIs, and practical examples of how these features can improve your code.',
        keyPoints: [
          'New array and object methods',
          'Enhanced async/await patterns',
          'Improved error handling mechanisms',
          'Performance improvements and optimizations'
        ],
        generatedAt: new Date(baseDate.getTime() - 12 * 60 * 60 * 1000),
      },
      quiz: {
        id: 'quiz-5',
        questions: [
          {
            id: 'q5-1',
            question: 'Which ES2024 feature improves array manipulation?',
            options: [
              'Array.prototype.with()',
              'Array.prototype.findLast()',
              'Array.prototype.toReversed()',
              'All of the above'
            ],
            correctAnswer: 3,
            explanation: 'ES2024 introduced several new array methods that provide immutable alternatives to existing methods.'
          }
        ]
      }
    }
  ];
}

export function getDemoPlaylists(): Playlist[] {
  const baseDate = new Date();
  
  return [
    {
      id: 'demo-playlist-1',
      name: 'Frontend Development Fundamentals',
      description: 'Essential videos for modern frontend development including React, TypeScript, and JavaScript.',
      videoIds: ['demo-video-1', 'demo-video-2', 'demo-video-5'],
      createdAt: new Date(baseDate.getTime() - 7 * 24 * 60 * 60 * 1000),
      color: '#3B82F6'
    },
    {
      id: 'demo-playlist-2',
      name: 'CSS & Design',
      description: 'Master modern CSS techniques and design principles.',
      videoIds: ['demo-video-3'],
      createdAt: new Date(baseDate.getTime() - 3 * 24 * 60 * 60 * 1000),
      color: '#10B981'
    },
    {
      id: 'demo-playlist-3',
      name: 'Backend & Performance',
      description: 'Server-side development and optimization techniques.',
      videoIds: ['demo-video-4'],
      createdAt: new Date(baseDate.getTime() - 1 * 24 * 60 * 60 * 1000),
      color: '#F59E0B'
    }
  ];
}