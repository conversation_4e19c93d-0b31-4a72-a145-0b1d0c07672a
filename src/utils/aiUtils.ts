import { AISummary, Quiz, Question } from '../types';
import { useApp } from '../contexts/AppContext';

// Get OpenAI configuration from user preferences or environment variables
function getOpenAIConfig() {
  // Try to get from localStorage (user preferences)
  try {
    const savedUser = localStorage.getItem('amiLearning_user');
    if (savedUser) {
      const user = JSON.parse(savedUser);
      if (user.preferences?.openaiApiKey) {
        return {
          apiKey: user.preferences.openaiApiKey,
          model: user.preferences.openaiModel || 'gpt-3.5-turbo',
          maxTokens: user.preferences.openaiMaxTokens || 500,
        };
      }
    }
  } catch (error) {
    console.warn('Error reading user preferences:', error);
  }
  
  // Fallback to environment variables
  return {
    apiKey: import.meta.env.VITE_OPENAI_API_KEY,
    model: import.meta.env.VITE_OPENAI_MODEL || 'gpt-3.5-turbo',
    maxTokens: parseInt(import.meta.env.VITE_OPENAI_MAX_TOKENS || '500'),
  };
}

// Adaptive content generation based on user's learning journey
export async function generateVideoSummary(videoUrl: string, title: string): Promise<AISummary> {
  const config = getOpenAIConfig();
  
  if (!config.apiKey) {
    console.warn('OpenAI API key not configured. Returning mock summary.');
    // Fallback to mock data if API key is missing
    return {
      id: crypto.randomUUID(),
      content: `Mock Summary: OpenAI API key not configured. This video "${title}" would normally be analyzed for personalized content. Please add your OpenAI API key in Settings > API Settings to enable AI features.`,
      keyPoints: ['Configure OpenAI API key in Settings', 'Recommended: GPT-3.5-turbo for cost efficiency'],
      generatedAt: new Date(),
    };
  }

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: OPENAI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are an adaptive learning assistant. Analyze video content (from URL and title) and create a concise, personalized summary with key bullet points. Adapt to the user\'s learning journey and current skill level if context were provided. Focus on extracting educational value.'
          },
          {
            role: 'user',
            content: `Analyze this video titled "${title}" (URL: ${videoUrl}) and provide a summary. Extract 3-5 key learning points as a bulleted list.`
          }
        ],
        // Adjust max_tokens for summary length
        max_tokens: Math.min(config.maxTokens, 800), // Cap summaries at 800 tokens
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({})); // Catch if response isn't JSON
      console.error('OpenAI API error for summary:', response.status, errorData);
      throw new Error(`OpenAI API request failed with status ${response.status}: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const choice = data.choices?.[0]?.message?.content;

    if (!choice) {
      console.error('OpenAI API response for summary missing content:', data);
      throw new Error('Invalid response structure from OpenAI API for summary.');
    }

    // Basic parsing of the response (assuming summary and key points are in the text)
    // This might need more sophisticated parsing based on actual OpenAI output format
    const contentParts = choice.split('\n\n');
    const summaryContent = contentParts[0] || 'Summary not available.';
    const keyPoints = contentParts.slice(1).map((pt: string) => pt.replace(/^- /, '').trim()).filter(Boolean);

    // If keypoints are not separated by double newlines, try single newline and bullet points
    if (keyPoints.length === 0 && choice.includes('\n- ')) {
        const lines = choice.split('\n');
        let mainContent = '';
        const points: string[] = [];
        lines.forEach((line: string) => {
            if (line.startsWith('- ')) {
                points.push(line.substring(2).trim());
            } else {
                mainContent += (mainContent ? '\n' : '') + line;
            }
        });
        return {
            id: crypto.randomUUID(),
            content: mainContent.trim() || 'Summary not available.',
            keyPoints: points.length > 0 ? points : ['Key points not extracted.'],
            generatedAt: new Date(),
        };
    }


    return {
      id: crypto.randomUUID(),
      content: summaryContent,
      keyPoints: keyPoints.length > 0 ? keyPoints : ['Key points not extracted.'],
      generatedAt: new Date(),
    };

  } catch (error) {
    console.error('Failed to generate video summary:', error);
    // Fallback to a mock/error summary
    return {
      id: crypto.randomUUID(),
      content: `Error generating summary for "${title}". Please try again later.`,
      keyPoints: ['Content analysis failed.'],
      generatedAt: new Date(),
    };
  }
}

export async function generateQuiz(videoUrl: string, title: string): Promise<Quiz> {
  const config = getOpenAIConfig();
  
  if (!config.apiKey) {
    console.warn('OpenAI API key not configured. Returning mock quiz.');
    // Fallback to mock data if API key is missing
    return {
      id: crypto.randomUUID(),
      questions: [{
        id: crypto.randomUUID(),
        question: `Mock Quiz: Configure OpenAI API key in Settings to generate a real quiz for "${title}".`,
        options: ['Go to Settings', 'Add API Key', 'Enable AI Features', 'All of the above'],
        correctAnswer: 0,
        explanation: 'Add your OpenAI API key in Settings > API Settings to enable AI-generated quizzes.'
      }],
    };
  }

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: OPENAI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are an adaptive learning assistant. Create 3-5 multiple-choice quiz questions based on the provided video content (URL and title). Each question should have 4 options and a correct answer index (0-3). Provide a brief explanation for the correct answer. Format the output as a JSON array of question objects. Each object should have: id (string), question (string), options (array of 4 strings), correctAnswer (number 0-3), explanation (string).'
          },
          {
            role: 'user',
            content: `Generate a JSON array of 3-5 quiz questions for the video titled "${title}" (URL: ${videoUrl}). Follow the specified JSON structure for each question object.`
          }
        ],
        // Request JSON output if supported by the model version
        response_format: { type: "json_object" },
        max_tokens: config.maxTokens,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('OpenAI API error for quiz:', response.status, errorData);
      throw new Error(`OpenAI API request failed with status ${response.status}: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    // Assuming the response itself is the JSON array of questions, or it's nested under a key like "questions"
    // Adjust data.questions if the API returns { "questions": [...] }
    let questions;
    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
        try {
            const content = JSON.parse(data.choices[0].message.content);
            questions = content.questions || content; // If content is {questions: []} or just []
        } catch (e) {
            console.error('Failed to parse quiz questions JSON from choices:', e);
            throw new Error('Invalid JSON response for quiz questions from OpenAI API.');
        }
    } else {
        console.error('OpenAI API response for quiz missing content or choices structure is unexpected:', data);
        throw new Error('Invalid response structure from OpenAI API for quiz.');
    }


    if (!Array.isArray(questions) || questions.some((q: any) => !q.question || !q.options || q.correctAnswer === undefined)) {
      console.error('OpenAI API response for quiz is not a valid questions array:', questions);
      throw new Error('Invalid quiz data structure from OpenAI API.');
    }

    // Ensure IDs are unique if not provided by API
    const validatedQuestions = questions.map((q: any) => ({
        ...q,
        id: q.id || crypto.randomUUID(),
    }));

    return {
      id: crypto.randomUUID(),
      questions: validatedQuestions,
    };

  } catch (error) {
    console.error('Failed to generate quiz:', error);
    // Fallback to a mock/error quiz
    return {
      id: crypto.randomUUID(),
      questions: [{
        id: crypto.randomUUID(),
        question: `Error generating quiz for "${title}". Please try again later.`,
        options: ['Retry', 'Cancel'],
        correctAnswer: 0,
        explanation: 'Quiz generation failed.'
      }],
    };
  }
}

export function calculateQuizScore(quiz: Quiz): number {
  if (!quiz.userAnswers) return 0;
  
  const correctAnswers = quiz.userAnswers.filter(answer => answer.isCorrect).length;
  return Math.round((correctAnswers / quiz.questions.length) * 100);
}