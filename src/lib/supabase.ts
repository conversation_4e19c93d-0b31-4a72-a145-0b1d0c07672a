import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database';
import { checkRateLimit, recordRequest } from '../utils/rateLimiter';
import { getSecureConfig } from '../utils/envValidation';

// Get validated environment configuration
const config = getSecureConfig();
const supabaseUrl = config.supabase.url;
const supabaseAnonKey = config.supabase.anonKey;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Auth helper functions
export const auth = {
  signUp: async (email: string, password: string, name: string) => {
    // Check rate limit before attempting signup
    const rateLimitCheck = checkRateLimit('auth-signup');
    if (!rateLimitCheck.allowed) {
      return {
        data: null,
        error: {
          message: `Too many signup attempts. Please try again in ${rateLimitCheck.retryAfter} seconds.`,
          status: 429
        }
      };
    }

    const userData: any = { name };

    // Set default preferences without sensitive data
    userData.preferences = {
      openaiModel: 'gpt-3.5-turbo',
      openaiMaxTokens: 500,
      darkMode: false,
      focusMode: false,
      autoplay: true,
      notifications: true,
      hasCompletedOnboarding: false
    };

    try {
      // Determine the correct redirect URL based on environment
      let redirectUrl;
      if (window.location.hostname === 'localhost') {
        redirectUrl = 'http://localhost:5173/auth/callback';
      } else if (window.location.hostname.includes('vercel.app')) {
        redirectUrl = 'https://amilearning-bolt.vercel.app/auth/callback';
      } else {
        redirectUrl = `${window.location.origin}/auth/callback`;
      }

      console.log('Using redirect URL:', redirectUrl);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
          emailRedirectTo: redirectUrl
        }
      });

      // Record the request for rate limiting
      recordRequest('auth-signup');

      return { data, error };
    } catch (error: any) {
      // Record failed attempts too
      recordRequest('auth-signup');
      return { data: null, error };
    }
  },

  signIn: async (email: string, password: string) => {
    // Check rate limit before attempting signin
    const rateLimitCheck = checkRateLimit('auth-signin');
    if (!rateLimitCheck.allowed) {
      return {
        data: null,
        error: {
          message: `Too many login attempts. Please try again in ${rateLimitCheck.retryAfter} seconds.`,
          status: 429
        }
      };
    }

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      // Record the request for rate limiting
      recordRequest('auth-signin');

      return { data, error };
    } catch (error: any) {
      // Record failed attempts too
      recordRequest('auth-signin');
      return { data: null, error };
    }
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  getCurrentUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    return { user, error };
  },

  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback);
  }
};