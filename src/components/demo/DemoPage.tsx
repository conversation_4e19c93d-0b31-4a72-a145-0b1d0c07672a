import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Play, 
  Pause, 
  Brain, 
  BookOpen, 
  Award, 
  CheckCircle,
  Clock,
  Target,
  Sparkles,
  Video,
  Users,
  TrendingUp,
  Star
} from 'lucide-react';

interface DemoPageProps {
  onBack: () => void;
}

export function DemoPage({ onBack }: DemoPageProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);

  const demoSteps = [
    {
      title: "Add Your Learning Videos",
      description: "Simply paste any YouTube or video URL to get started",
      component: "video-upload"
    },
    {
      title: "Intelligent Content Analysis",
      description: "Get instant summaries that adapt to your learning style and pace",
      component: "ai-summary"
    },
    {
      title: "Personalized Quizzes",
      description: "Test your knowledge with questions tailored to your learning journey",
      component: "quiz"
    },
    {
      title: "Track Your Progress",
      description: "Visual progress tracking with achievements and badges",
      component: "progress"
    },
    {
      title: "Distraction-Free Learning",
      description: "Focus mode eliminates distractions for better concentration",
      component: "focus-mode"
    }
  ];

  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            setIsPlaying(false);
            return 100;
          }
          return prev + 2;
        });
      }, 100);
      return () => clearInterval(interval);
    }
  }, [isPlaying]);

  const renderDemoComponent = () => {
    switch (demoSteps[currentStep].component) {
      case "video-upload":
        return (
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="space-y-6">
              <div className="flex items-center gap-3 p-4 bg-white/20 rounded-xl">
                <Video className="h-6 w-6 text-blue-400" />
                <input
                  type="text"
                  placeholder="https://youtube.com/watch?v=example"
                  className="flex-1 bg-transparent text-white placeholder-white/60 outline-none"
                  value="https://youtube.com/watch?v=dQw4w9WgXcQ"
                  readOnly
                />
              </div>
              <button className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 rounded-xl font-semibold flex items-center justify-center gap-2">
                <Sparkles className="h-5 w-5" />
                Generate Summary & Quiz
              </button>
            </div>
          </div>
        );

      case "ai-summary":
        return (
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="flex items-center gap-3 mb-6">
              <Brain className="h-6 w-6 text-purple-400" />
              <h3 className="text-xl font-semibold text-white">Adaptive Summary</h3>
            </div>
            <div className="space-y-4">
              <p className="text-white/80 leading-relaxed">
                Based on your learning journey, this video covers essential concepts in modern web development, focusing on React hooks and state management patterns. The analysis suggests this aligns with your current skill level and learning goals.
              </p>
              <div>
                <h4 className="text-white font-medium mb-3">Key Points:</h4>
                <ul className="space-y-2">
                  {[
                    "Introduction to React hooks and their benefits",
                    "State management with useState and useEffect",
                    "Custom hooks for reusable logic",
                    "Performance optimization techniques"
                  ].map((point, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400 mt-1 flex-shrink-0" />
                      <span className="text-white/80">{point}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        );

      case "quiz":
        return (
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="flex items-center gap-3 mb-6">
              <BookOpen className="h-6 w-6 text-green-400" />
              <h3 className="text-xl font-semibold text-white">Personalized Quiz</h3>
            </div>
            <div className="space-y-6">
              <div>
                <h4 className="text-white font-medium mb-4">
                  1. What is the primary benefit of using React hooks?
                </h4>
                <div className="space-y-3">
                  {[
                    "Better performance",
                    "Simpler state management",
                    "Easier testing",
                    "All of the above"
                  ].map((option, index) => (
                    <button
                      key={index}
                      className={`w-full text-left p-3 rounded-xl border transition-all duration-200 ${
                        index === 3 
                          ? 'border-green-400 bg-green-400/20 text-white' 
                          : 'border-white/20 bg-white/10 text-white/80 hover:bg-white/20'
                      }`}
                    >
                      {option}
                    </button>
                  ))}
                </div>
              </div>
              <div className="flex items-center gap-2 text-green-400">
                <CheckCircle className="h-5 w-5" />
                <span>Correct! Great job!</span>
              </div>
            </div>
          </div>
        );

      case "progress":
        return (
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="grid grid-cols-2 gap-6">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                  <Video className="h-10 w-10 text-white" />
                </div>
                <p className="text-2xl font-bold text-white">12</p>
                <p className="text-white/60">Videos Watched</p>
              </div>
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                  <Award className="h-10 w-10 text-white" />
                </div>
                <p className="text-2xl font-bold text-white">850</p>
                <p className="text-white/60">Points Earned</p>
              </div>
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                  <Target className="h-10 w-10 text-white" />
                </div>
                <p className="text-2xl font-bold text-white">7</p>
                <p className="text-white/60">Day Streak</p>
              </div>
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-3">
                  <Clock className="h-10 w-10 text-white" />
                </div>
                <p className="text-2xl font-bold text-white">24h</p>
                <p className="text-white/60">Total Time</p>
              </div>
            </div>
          </div>
        );

      case "focus-mode":
        return (
          <div className="bg-black/80 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
            <div className="text-center space-y-6">
              <div className="w-24 h-24 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto">
                <Play className="h-12 w-12 text-white ml-1" />
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-white mb-2">Focus Mode Active</h3>
                <p className="text-white/60">
                  All distractions removed. Pure learning experience.
                </p>
              </div>
              <div className="w-full bg-white/20 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <p className="text-white/80">{progress}% completed</p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        {/* Floating Orbs */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-blue-400/30 to-purple-400/30 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-32 w-24 h-24 bg-gradient-to-r from-pink-400/20 to-red-400/20 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-32 w-40 h-40 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-20 w-28 h-28 bg-gradient-to-r from-yellow-400/25 to-orange-400/25 rounded-full animate-pulse delay-3000"></div>
        
        {/* Moving Particles */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full animate-ping"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-purple-900/10 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="flex items-center justify-between mb-12">
          <button
            onClick={onBack}
            className="flex items-center gap-2 text-white/80 hover:text-white transition-colors duration-200"
          >
            <ArrowLeft className="h-5 w-5" />
            Back to Home
          </button>
          <div className="flex items-center gap-2">
            <Brain className="h-8 w-8 text-purple-400" />
            <span className="text-2xl font-bold text-white">AmILearning Demo</span>
          </div>
        </div>

        {/* Demo Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Demo Steps */}
          <div className="space-y-8">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                See AmILearning
                <br />
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  In Action
                </span>
              </h1>
              <p className="text-xl text-white/80 mb-8">
                Experience how our intelligent platform analyzes your learning patterns and adapts to create a personalized educational journey.
              </p>
            </div>

            {/* Step Navigation */}
            <div className="space-y-4">
              {demoSteps.map((step, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentStep(index)}
                  className={`w-full text-left p-4 rounded-xl transition-all duration-300 ${
                    currentStep === index
                      ? 'bg-white/20 border-2 border-purple-400'
                      : 'bg-white/10 border-2 border-transparent hover:bg-white/15'
                  }`}
                >
                  <div className="flex items-center gap-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      currentStep === index
                        ? 'bg-purple-500 text-white'
                        : 'bg-white/20 text-white/60'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <h3 className="font-semibold text-white">{step.title}</h3>
                      <p className="text-sm text-white/60">{step.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {/* Demo Controls */}
            <div className="flex items-center gap-4">
              <button
                onClick={() => setIsPlaying(!isPlaying)}
                className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 flex items-center gap-2"
              >
                {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
                {isPlaying ? 'Pause Demo' : 'Start Demo'}
              </button>
              <button
                onClick={() => {
                  setProgress(0);
                  setIsPlaying(false);
                }}
                className="px-6 py-3 border-2 border-white/20 text-white rounded-xl hover:bg-white/10 transition-all duration-300"
              >
                Reset
              </button>
            </div>
          </div>

          {/* Right Side - Demo Component */}
          <div className="relative">
            {/* Demo Screen */}
            <div className="relative">
              {renderDemoComponent()}
              
              {/* Floating Stats */}
              <div className="absolute -top-6 -right-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                95% Success Rate
              </div>
              
              <div className="absolute -bottom-6 -left-6 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                <Users className="h-4 w-4" />
                10K+ Learners
              </div>
            </div>
          </div>
        </div>

        {/* Features Showcase */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Why Choose AmILearning?
            </h2>
            <p className="text-xl text-white/80">
              Powerful features designed for modern learners
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Brain,
                title: 'Adaptive Intelligence',
                description: 'Advanced analysis adapts content to your learning journey and suggests optimal paths',
                color: 'from-purple-500 to-pink-500'
              },
              {
                icon: Target,
                title: 'Progress Tracking',
                description: 'Visual analytics and achievement system keeps you motivated',
                color: 'from-blue-500 to-cyan-500'
              },
              {
                icon: Star,
                title: 'Distraction-Free',
                description: 'Focus mode eliminates distractions for better learning',
                color: 'from-yellow-500 to-orange-500'
              }
            ].map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300"
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mb-6`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-white/70">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-3xl p-12 border border-white/20">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Transform Your Learning?
            </h2>
            <p className="text-xl text-white/80 mb-8">
              Join thousands of learners who are already using AmILearning to accelerate their education.
            </p>
            <button
              onClick={onBack}
              className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              Start Learning Free
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}