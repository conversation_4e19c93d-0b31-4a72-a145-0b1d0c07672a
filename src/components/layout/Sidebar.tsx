import React from 'react';
import { 
  Home, 
  Video, 
  BookOpen, 
  Trophy, 
  Settings, 
  Plus,
  Moon,
  Sun,
  Focus,
  ChevronLeft,
  ChevronRight,
  Pin,
  PinOff
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';

interface SidebarProps {
  currentView: string;
  onViewChange: (view: string) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  isPinned: boolean;
  onTogglePin: () => void;
}

export function Sidebar({ 
  currentView, 
  onViewChange, 
  isCollapsed, 
  onToggleCollapse, 
  isPinned, 
  onTogglePin 
}: SidebarProps) {
  const { state, dispatch } = useApp();

  const handleToggleDarkMode = () => {
    dispatch({
      type: 'UPDATE_PREFERENCES',
      payload: { darkMode: !state.user?.preferences.darkMode }
    });
  };

  const handleToggleFocusMode = () => {
    dispatch({
      type: 'UPDATE_PREFERENCES',
      payload: { focusMode: !state.user?.preferences.focusMode }
    });
  };

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'videos', label: 'My Videos', icon: Video },
    { id: 'learning-paths', label: 'Learning Paths', icon: BookOpen },
    { id: 'playlists', label: 'Playlists', icon: BookOpen },
    { id: 'progress', label: 'Progress', icon: Trophy },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className={`
      ${isCollapsed ? 'w-16' : 'w-64'} h-screen bg-white border-r border-gray-200 flex flex-col transition-all duration-300 fixed left-0 top-0 z-40
      ${state.user?.preferences.darkMode ? 'dark:bg-gray-900 dark:border-gray-700' : ''}
      ${state.user?.preferences.focusMode ? 'opacity-80' : ''}
    `}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                AmILearning
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Welcome back, {state.user?.name}
              </p>
            </div>
          )}
          
          {/* Collapse/Expand and Pin Controls */}
          <div className="flex items-center gap-1">
            {!isCollapsed && (
              <button
                onClick={onTogglePin}
                className={`p-1.5 rounded-md transition-colors duration-200 ${
                  isPinned 
                    ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' 
                    : 'text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300'
                }`}
                title={isPinned ? 'Unpin sidebar' : 'Pin sidebar'}
              >
                {isPinned ? <Pin className="h-4 w-4" /> : <PinOff className="h-4 w-4" />}
              </button>
            )}
            <button
              onClick={onToggleCollapse}
              className="p-1.5 rounded-md text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors duration-200"
              title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      {!isCollapsed && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => onViewChange('add-video')}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Video
          </button>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;
            
            return (
              <li key={item.id}>
                <button
                  onClick={() => onViewChange(item.id)}
                  className={`
                    w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 relative group
                    ${isActive 
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' 
                      : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'
                    }
                  `}
                  title={isCollapsed ? item.label : ''}
                >
                  <Icon className="h-5 w-5" />
                  {!isCollapsed && item.label}
                  
                  {/* Tooltip for collapsed state */}
                  {isCollapsed && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.label}
                    </div>
                  )}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Theme Controls */}
      {!isCollapsed ? (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Theme
            </span>
            <button
              onClick={handleToggleDarkMode}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
            >
              {state.user?.preferences.darkMode ? (
                <Sun className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              ) : (
                <Moon className="h-4 w-4 text-gray-600" />
              )}
            </button>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Focus Mode
            </span>
            <button
              onClick={handleToggleFocusMode}
              className={`
                p-2 rounded-lg transition-colors duration-200
                ${state.user?.preferences.focusMode 
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' 
                  : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                }
              `}
            >
              <Focus className="h-4 w-4" />
            </button>
          </div>
        </div>
      ) : (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-2">
          <button
            onClick={handleToggleDarkMode}
            className="w-full p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 group relative"
            title="Toggle theme"
          >
            {state.user?.preferences.darkMode ? (
              <Sun className="h-4 w-4 text-gray-600 dark:text-gray-400 mx-auto" />
            ) : (
              <Moon className="h-4 w-4 text-gray-600 mx-auto" />
            )}
            <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              Toggle theme
            </div>
          </button>
          
          <button
            onClick={handleToggleFocusMode}
            className={`
              w-full p-2 rounded-lg transition-colors duration-200 group relative
              ${state.user?.preferences.focusMode 
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' 
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
              }
            `}
            title="Toggle focus mode"
          >
            <Focus className="h-4 w-4 mx-auto" />
            <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              Toggle focus mode
            </div>
          </button>
        </div>
      )}

      {/* User Points */}
      {!isCollapsed ? (
        <div className="p-4 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Points</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {state.user?.points || 0}
              </p>
            </div>
            <Trophy className="h-8 w-8 text-yellow-500" />
          </div>
        </div>
      ) : (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 text-center group relative">
          <Trophy className="h-6 w-6 text-yellow-500 mx-auto" />
          <p className="text-xs font-bold text-blue-600 dark:text-blue-400 mt-1">
            {state.user?.points || 0}
          </p>
          <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            Points: {state.user?.points || 0}
          </div>
        </div>
      )}
    </div>
  );
}