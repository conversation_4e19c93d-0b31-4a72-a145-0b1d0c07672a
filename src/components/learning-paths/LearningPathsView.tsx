import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Clock, 
  Users, 
  Star, 
  Play, 
  BookOpen,
  TrendingUp,
  ChevronRight,
  Loader
} from 'lucide-react';
import { LearningPath, LearningCategory } from '../../types';
import { generateLearningPath, getPopularLearningCategories } from '../../utils/perplexityApi';
import { useApp } from '../../contexts/AppContext';

export function LearningPathsView() {
  const { state } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [categories, setCategories] = useState<LearningCategory[]>([]);
  const [learningPaths, setLearningPaths] = useState<LearningPath[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    loadCategories();
    loadPopularPaths();
  }, []);

  const loadCategories = async () => {
    try {
      const cats = await getPopularLearningCategories();
      setCategories(cats);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadPopularPaths = async () => {
    setLoading(true);
    try {
      // Load a few popular learning paths
      const popularCategories = ['Software Development', 'Data Science', 'Digital Marketing'];
      const paths = await Promise.all(
        popularCategories.map(category => 
          generateLearningPath(category, 'beginner', 10)
        )
      );
      setLearningPaths(paths);
    } catch (error) {
      console.error('Error loading learning paths:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    try {
      const difficulty = selectedDifficulty === 'all' ? 'beginner' : selectedDifficulty;
      const path = await generateLearningPath(searchQuery, difficulty as any, 10);
      setLearningPaths([path, ...learningPaths]);
    } catch (error) {
      console.error('Error searching learning paths:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySelect = async (categoryId: string) => {
    if (categoryId === 'all') {
      loadPopularPaths();
      return;
    }

    const category = categories.find(cat => cat.id === categoryId);
    if (!category) return;

    setLoading(true);
    try {
      const difficulty = selectedDifficulty === 'all' ? 'beginner' : selectedDifficulty;
      const path = await generateLearningPath(category.name, difficulty as any, 10);
      setLearningPaths([path]);
    } catch (error) {
      console.error('Error loading category paths:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPaths = learningPaths.filter(path => {
    const matchesCategory = selectedCategory === 'all' || path.category.toLowerCase().includes(selectedCategory.toLowerCase());
    const matchesDifficulty = selectedDifficulty === 'all' || path.difficulty === selectedDifficulty;
    const matchesSearch = !searchQuery || 
      path.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      path.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      path.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesDifficulty && matchesSearch;
  });

  return (
    <div className={`
      p-6 space-y-6
      ${state.user?.preferences.darkMode ? 'dark:bg-gray-900' : 'bg-gray-50'}
      min-h-screen
    `}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Learning Paths</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Curated learning journeys powered by AI research
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search for learning topics (e.g., 'React development', 'Machine learning')"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          
          {/* Search Button */}
          <button
            onClick={handleSearch}
            disabled={loading || !searchQuery.trim()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors duration-200 flex items-center gap-2"
          >
            {loading ? (
              <Loader className="h-4 w-4 animate-spin" />
            ) : (
              <Search className="h-4 w-4" />
            )}
            Generate Path
          </button>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    handleCategorySelect(e.target.value);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Difficulty Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Difficulty
                </label>
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="all">All Levels</option>
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Popular Categories */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Popular Categories
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => {
                setSelectedCategory(category.id);
                handleCategorySelect(category.id);
              }}
              className="p-4 text-center bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
            >
              <div className="text-2xl mb-2">{category.icon}</div>
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {category.name}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Learning Paths */}
      <div className="space-y-6">
        {loading && learningPaths.length === 0 ? (
          <div className="text-center py-12">
            <Loader className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              Researching and curating learning content...
            </p>
          </div>
        ) : filteredPaths.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredPaths.map((path) => (
              <div
                key={path.id}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden"
              >
                {/* Header */}
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        {path.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {path.description}
                      </p>
                    </div>
                    {path.isPopular && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded-full text-xs">
                        <Star className="h-3 w-3" />
                        Popular
                      </div>
                    )}
                  </div>

                  {/* Meta Info */}
                  <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {path.estimatedDuration}h
                    </div>
                    <div className="flex items-center gap-1">
                      <BookOpen className="h-4 w-4" />
                      {path.videos.length} videos
                    </div>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="h-4 w-4" />
                      {path.difficulty}
                    </div>
                    {path.enrollmentCount && (
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        {path.enrollmentCount}
                      </div>
                    )}
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mt-3">
                    {path.tags.slice(0, 4).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Learning Objectives */}
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    What you'll learn:
                  </h4>
                  <ul className="space-y-2">
                    {path.learningObjectives.slice(0, 3).map((objective, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        {objective}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Videos Preview */}
                <div className="p-6">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Course Content:
                  </h4>
                  <div className="space-y-2">
                    {path.videos.slice(0, 3).map((video, index) => (
                      <div key={video.id} className="flex items-center gap-3 text-sm">
                        <div className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </div>
                        <div className="flex-1 truncate">
                          <span className="text-gray-900 dark:text-white">
                            {video.title}
                          </span>
                        </div>
                        <span className="text-gray-500 dark:text-gray-400">
                          {Math.round(video.duration / 60)}m
                        </span>
                      </div>
                    ))}
                    {path.videos.length > 3 && (
                      <div className="text-sm text-gray-500 dark:text-gray-400 ml-9">
                        +{path.videos.length - 3} more videos
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Button */}
                <div className="p-6 pt-0">
                  <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center gap-2">
                    <Play className="h-4 w-4" />
                    Start Learning Path
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No learning paths found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Try searching for a specific topic or select a different category
            </p>
            <button
              onClick={loadPopularPaths}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Browse popular paths
            </button>
          </div>
        )}
      </div>
    </div>
  );
}