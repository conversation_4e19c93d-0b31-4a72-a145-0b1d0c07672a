import React, { useState } from 'react';
import { Plus, Play, Edit3, Trash2, Video, Clock } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { databaseService } from '../../services/database';
import { Playlist } from '../../types';

export function PlaylistsView() {
  const { state, dispatch } = useApp();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
  });

  const handleCreatePlaylist = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newPlaylist: Playlist = {
      id: crypto.randomUUID(),
      name: formData.name,
      description: formData.description,
      videoIds: [],
      createdAt: new Date(),
      color: formData.color,
    };

    if (state.isAuthenticated) {
      databaseService.addPlaylist(newPlaylist).then((savedPlaylist) => {
        dispatch({ type: 'ADD_PLAYLIST', payload: savedPlaylist });
      }).catch(error => {
        console.error('Error creating playlist:', error);
        alert('Failed to create playlist. Please try again.');
      });
    } else {
      dispatch({ type: 'ADD_PLAYLIST', payload: newPlaylist });
    }
    
    setFormData({ name: '', description: '', color: '#3B82F6' });
    setShowCreateForm(false);
  };

  const getPlaylistVideos = (playlist: Playlist) => {
    return state.videos.filter(video => playlist.videoIds.includes(video.id));
  };

  const getTotalDuration = (playlist: Playlist) => {
    const videos = getPlaylistVideos(playlist);
    return videos.reduce((total, video) => total + video.duration, 0);
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  return (
    <div className={`
      p-6 space-y-6
      ${state.user?.preferences.darkMode ? 'dark:bg-gray-900' : 'bg-gray-50'}
      min-h-screen
    `}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Playlists</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Organize your videos into collections
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Playlist
        </button>
      </div>

      {/* Playlists Grid */}
      {state.playlists.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {state.playlists.map((playlist) => {
            const videos = getPlaylistVideos(playlist);
            const totalDuration = getTotalDuration(playlist);
            
            return (
              <div
                key={playlist.id}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden"
              >
                {/* Header with color */}
                <div 
                  className="h-24 flex items-end p-4"
                  style={{ backgroundColor: playlist.color }}
                >
                  <h3 className="text-white font-semibold text-lg">
                    {playlist.name}
                  </h3>
                </div>

                {/* Content */}
                <div className="p-4">
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    {playlist.description}
                  </p>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <div className="flex items-center gap-1">
                      <Video className="h-4 w-4" />
                      {videos.length} video{videos.length !== 1 ? 's' : ''}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {formatDuration(totalDuration)}
                    </div>
                  </div>

                  {/* Video previews */}
                  {videos.length > 0 && (
                    <div className="space-y-2 mb-4">
                      {videos.slice(0, 3).map((video) => (
                        <div key={video.id} className="flex items-center gap-3 text-sm">
                          <img
                            src={video.thumbnail}
                            alt={video.title}
                            className="w-8 h-6 object-cover rounded"
                            onError={(e) => {
                              e.currentTarget.src = 'https://images.pexels.com/photos/5428836/pexels-photo-5428836.jpeg?auto=compress&cs=tinysrgb&w=64&h=48&dpr=1';
                            }}
                          />
                          <span className="text-gray-700 dark:text-gray-300 truncate">
                            {video.title}
                          </span>
                        </div>
                      ))}
                      {videos.length > 3 && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 ml-11">
                          +{videos.length - 3} more videos
                        </p>
                      )}
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-2">
                    <button
                      disabled={videos.length === 0}
                      className="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200 flex items-center justify-center gap-2 text-sm"
                    >
                      <Play className="h-4 w-4" />
                      Play All
                    </button>
                    <button className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                      <Edit3 className="h-4 w-4" />
                    </button>
                    <button 
                      onClick={() => {
                        if (window.confirm('Are you sure you want to delete this playlist?')) {
                          if (state.isAuthenticated) {
                            databaseService.deletePlaylist(playlist.id).then(() => {
                              dispatch({ type: 'DELETE_PLAYLIST', payload: playlist.id });
                            }).catch(error => {
                              console.error('Error deleting playlist:', error);
                              alert('Failed to delete playlist. Please try again.');
                            });
                          } else {
                            dispatch({ type: 'DELETE_PLAYLIST', payload: playlist.id });
                          }
                        }
                      }}
                      className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors duration-200"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md mx-auto">
            <Play className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No playlists yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Create your first playlist to organize your learning videos
            </p>
          </div>
        </div>
      )}

      {/* Create Playlist Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-md">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Create New Playlist
            </h2>

            <form onSubmit={handleCreatePlaylist} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter playlist name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter description"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Color
                </label>
                <div className="flex gap-2">
                  {['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'].map(color => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, color }))}
                      className={`w-8 h-8 rounded-lg border-2 ${
                        formData.color === color ? 'border-gray-400' : 'border-transparent'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                  Create Playlist
                </button>
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}