import React from 'react';
import { TrendingUp, Calendar, Target, Award, Clock, CheckCircle, Play, Book } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { getProgressStatus } from '../../utils/videoUtils';

export function ProgressView() {
  const { state } = useApp();

  // Calculate stats
  const totalVideos = state.videos.length;
  const completedVideos = state.videos.filter(v => getProgressStatus(v.progress.completionPercentage) === 'completed').length;
  const inProgressVideos = state.videos.filter(v => getProgressStatus(v.progress.completionPercentage) === 'in_progress').length;
  const totalWatchTime = state.videos.reduce((acc, v) => acc + v.progress.watchTime, 0);

  // Weekly progress (mock data)
  const weeklyProgress = [
    { day: 'Mon', hours: 2.5 },
    { day: 'Tue', hours: 1.8 },
    { day: 'Wed', hours: 3.2 },
    { day: 'Thu', hours: 2.1 },
    { day: 'Fri', hours: 4.5 },
    { day: 'Sat', hours: 3.8 },
    { day: 'Sun', hours: 2.9 },
  ];

  const maxHours = Math.max(...weeklyProgress.map(d => d.hours));

  // Learning streak (mock)
  const currentStreak = 7;
  const longestStreak = 15;

  // Achievements
  const achievements = [
    {
      id: 1,
      title: 'First Video',
      description: 'Added your first video',
      icon: Play,
      earned: totalVideos > 0,
      date: totalVideos > 0 ? new Date().toLocaleDateString() : null,
    },
    {
      id: 2,
      title: 'Completion Champion',
      description: 'Completed 10 videos',
      icon: CheckCircle,
      earned: completedVideos >= 10,
      date: completedVideos >= 10 ? new Date().toLocaleDateString() : null,
    },
    {
      id: 3,
      title: 'Dedicated Learner',
      description: 'Watched 20+ hours',
      icon: Clock,
      earned: totalWatchTime >= 72000, // 20 hours
      date: totalWatchTime >= 72000 ? new Date().toLocaleDateString() : null,
    },
    {
      id: 4,
      title: 'Knowledge Seeker',
      description: 'Completed 5 quizzes',
      icon: Book,
      earned: false, // Would be calculated based on quiz completions
      date: null,
    },
  ];

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  return (
    <div className={`
      p-6 space-y-6
      ${state.user?.preferences.darkMode ? 'dark:bg-gray-900' : 'bg-gray-50'}
      min-h-screen
    `}>
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Learning Progress</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Track your learning journey and achievements
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Watch Time</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">
                {formatTime(totalWatchTime)}
              </p>
            </div>
            <Clock className="h-12 w-12 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed Videos</p>
              <p className="text-3xl font-bold text-green-600">{completedVideos}</p>
            </div>
            <CheckCircle className="h-12 w-12 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Streak</p>
              <p className="text-3xl font-bold text-orange-600">{currentStreak}</p>
            </div>
            <Target className="h-12 w-12 text-orange-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Points Earned</p>
              <p className="text-3xl font-bold text-purple-600">{state.user?.points || 0}</p>
            </div>
            <Award className="h-12 w-12 text-purple-500" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Progress Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            This Week's Progress
          </h2>
          <div className="space-y-4">
            {weeklyProgress.map((day, index) => (
              <div key={day.day} className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 w-8">
                  {day.day}
                </span>
                <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className="bg-blue-500 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${(day.hours / maxHours) * 100}%` }}
                  />
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-400 w-12 text-right">
                  {day.hours}h
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Learning Goals */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            Learning Goals
          </h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center gap-3">
                <Target className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="font-medium text-blue-800 dark:text-blue-200">Daily Goal</p>
                  <p className="text-sm text-blue-600 dark:text-blue-300">Watch 30 minutes</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-blue-600 dark:text-blue-300">
                  {Math.min(100, Math.round((weeklyProgress[weeklyProgress.length - 1].hours * 60) / 30 * 100))}%
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div>
                  <p className="font-medium text-green-800 dark:text-green-200">Weekly Goal</p>
                  <p className="text-sm text-green-600 dark:text-green-300">Complete 3 videos</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-green-600 dark:text-green-300">
                  {Math.min(100, Math.round((completedVideos / 3) * 100))}%
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="flex items-center gap-3">
                <TrendingUp className="h-8 w-8 text-purple-500" />
                <div>
                  <p className="font-medium text-purple-800 dark:text-purple-200">Monthly Goal</p>
                  <p className="text-sm text-purple-600 dark:text-purple-300">Earn 500 points</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-purple-600 dark:text-purple-300">
                  {Math.min(100, Math.round(((state.user?.points || 0) / 500) * 100))}%
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Achievements */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Achievements
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {achievements.map((achievement) => {
            const Icon = achievement.icon;
            return (
              <div
                key={achievement.id}
                className={`
                  p-4 rounded-lg border-2 transition-all duration-200
                  ${achievement.earned
                    ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
                    : 'bg-gray-50 border-gray-200 dark:bg-gray-700/50 dark:border-gray-600'
                  }
                `}
              >
                <Icon
                  className={`
                    h-8 w-8 mb-3
                    ${achievement.earned ? 'text-yellow-500' : 'text-gray-400 dark:text-gray-500'}
                  `}
                />
                <h3 className={`
                  font-medium mb-1
                  ${achievement.earned
                    ? 'text-yellow-800 dark:text-yellow-200'
                    : 'text-gray-600 dark:text-gray-400'
                  }
                `}>
                  {achievement.title}
                </h3>
                <p className={`
                  text-sm mb-2
                  ${achievement.earned
                    ? 'text-yellow-600 dark:text-yellow-300'
                    : 'text-gray-500 dark:text-gray-500'
                  }
                `}>
                  {achievement.description}
                </p>
                {achievement.earned && achievement.date && (
                  <p className="text-xs text-yellow-600 dark:text-yellow-400">
                    Earned: {achievement.date}
                  </p>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Recent Activity
        </h2>
        {state.videos.length > 0 ? (
          <div className="space-y-4">
            {state.videos
              .sort((a, b) => new Date(b.progress.lastWatched).getTime() - new Date(a.progress.lastWatched).getTime())
              .slice(0, 5)
              .map((video) => {
                const status = getProgressStatus(video.progress.completionPercentage);
                return (
                  <div key={video.id} className="flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <img
                      src={video.thumbnail}
                      alt={video.title}
                      className="w-16 h-12 object-cover rounded"
                      onError={(e) => {
                        e.currentTarget.src = 'https://images.pexels.com/photos/5428836/pexels-photo-5428836.jpeg?auto=compress&cs=tinysrgb&w=64&h=48&dpr=1';
                      }}
                    />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 dark:text-white truncate">
                        {video.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {status === 'completed' ? 'Completed' : `${video.progress.completionPercentage}% watched`}
                      </p>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {new Date(video.progress.lastWatched).toLocaleDateString()}
                    </div>
                  </div>
                );
              })}
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400 text-center py-8">
            No activity yet. Start watching videos to see your progress!
          </p>
        )}
      </div>
    </div>
  );
}