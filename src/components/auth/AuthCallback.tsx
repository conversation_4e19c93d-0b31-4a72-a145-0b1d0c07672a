import React, { useEffect, useState } from 'react';
import { CheckCircle, XCircle, Loader2, Home } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { supabase } from '../../lib/supabase';
import { secureStorage } from '../../utils/secureStorage';

type CallbackStatus = 'loading' | 'success' | 'error' | 'expired';

interface CallbackState {
  status: CallbackStatus;
  message: string;
  error?: string;
}

export function AuthCallback() {
  const { dispatch } = useApp();
  const [state, setState] = useState<CallbackState>({
    status: 'loading',
    message: 'Confirming your email...'
  });

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Check if we're on the wrong domain (Supabase redirect issue)
        if (window.location.hostname.includes('supabase.co')) {
          setState({
            status: 'error',
            message: 'Redirect configuration issue detected. Please check your Supabase project settings and ensure the redirect URL is properly configured.',
            error: 'The email confirmation is redirecting to the wrong domain. This needs to be fixed in your Supabase project settings.'
          });
          return;
        }

        // Get the current URL to check for auth parameters
        const urlParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        
        // Check for error parameters first
        const error = urlParams.get('error') || hashParams.get('error');
        const errorDescription = urlParams.get('error_description') || hashParams.get('error_description');
        
        if (error) {
          console.error('Auth callback error:', error, errorDescription);
          setState({
            status: 'error',
            message: 'Email confirmation failed',
            error: errorDescription || error
          });
          return;
        }

        // Check for auth tokens
        const accessToken = urlParams.get('access_token') || hashParams.get('access_token');
        const refreshToken = urlParams.get('refresh_token') || hashParams.get('refresh_token');
        const type = urlParams.get('type') || hashParams.get('type');

        if (type === 'signup' || accessToken) {
          // If we have tokens in the URL, try to set the session manually first
          if (accessToken && refreshToken) {
            const { data: sessionData, error: setSessionError } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });

            if (setSessionError) {
              console.error('Set session error:', setSessionError);
            } else if (sessionData.session) {
              // Session set successfully, proceed with success flow
              setState({
                status: 'success',
                message: 'Email confirmed successfully! Redirecting to your dashboard...'
              });

              // Handle pending API key storage
              const pendingApiKey = localStorage.getItem('pending_openai_key');
              if (pendingApiKey) {
                try {
                  const storeResult = await secureStorage.storeApiKey(pendingApiKey);
                  if (storeResult.success) {
                    console.log('API key stored securely after email confirmation');
                  } else {
                    console.warn('Failed to store pending API key:', storeResult.error);
                  }
                } catch (error) {
                  console.error('Error storing pending API key:', error);
                } finally {
                  // Always clear the pending key from localStorage
                  localStorage.removeItem('pending_openai_key');
                }
              }

              // Clear the URL parameters for security
              window.history.replaceState({}, document.title, window.location.pathname);

              // Redirect to dashboard after showing success message
              setTimeout(() => {
                window.location.href = '/';
              }, 2000);
              return;
            }
          }

          // Fallback: Process the authentication using getSession
          const { data, error: sessionError } = await supabase.auth.getSession();
          
          if (sessionError) {
            console.error('Session error:', sessionError);
            setState({
              status: 'error',
              message: 'Failed to establish session',
              error: sessionError.message
            });
            return;
          }

          if (data.session) {
            setState({
              status: 'success',
              message: 'Email confirmed successfully! Redirecting to your dashboard...'
            });

            // Handle pending API key storage
            const pendingApiKey = localStorage.getItem('pending_openai_key');
            if (pendingApiKey) {
              try {
                const storeResult = await secureStorage.storeApiKey(pendingApiKey);
                if (storeResult.success) {
                  console.log('API key stored securely after email confirmation');
                } else {
                  console.warn('Failed to store pending API key:', storeResult.error);
                }
              } catch (error) {
                console.error('Error storing pending API key:', error);
              } finally {
                // Always clear the pending key from localStorage
                localStorage.removeItem('pending_openai_key');
              }
            }

            // Clear the URL parameters for security
            window.history.replaceState({}, document.title, window.location.pathname);

            // Redirect to dashboard after showing success message
            setTimeout(() => {
              window.location.href = '/';
            }, 2000);
          } else {
            setState({
              status: 'error',
              message: 'No active session found',
              error: 'Please try signing in again'
            });
          }
        } else {
          setState({
            status: 'error',
            message: 'Invalid confirmation link',
            error: 'The confirmation link appears to be invalid or expired'
          });
        }
      } catch (error: any) {
        console.error('Callback processing error:', error);
        setState({
          status: 'error',
          message: 'An unexpected error occurred',
          error: error.message || 'Please try again later'
        });
      }
    };

    // Add a small delay to ensure Supabase has processed the URL
    const timer = setTimeout(handleAuthCallback, 500);
    
    return () => clearTimeout(timer);
  }, [dispatch]);

  const handleReturnHome = () => {
    window.location.href = '/';
  };

  const renderIcon = () => {
    switch (state.status) {
      case 'loading':
        return <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-12 w-12 text-green-500" />;
      case 'error':
      case 'expired':
        return <XCircle className="h-12 w-12 text-red-500" />;
      default:
        return <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />;
    }
  };

  const getStatusColor = () => {
    switch (state.status) {
      case 'loading':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
      case 'expired':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md text-center">
        <div className="mb-6">
          {renderIcon()}
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Email Confirmation
        </h1>
        
        <p className={`text-lg font-medium mb-2 ${getStatusColor()}`}>
          {state.message}
        </p>
        
        {state.error && (
          <p className="text-sm text-gray-600 mb-6">
            {state.error}
          </p>
        )}
        
        {(state.status === 'error' || state.status === 'expired') && (
          <div className="space-y-4">
            <button
              onClick={handleReturnHome}
              className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              <Home className="h-4 w-4" />
              Return to Home
            </button>
            
            <p className="text-xs text-gray-500">
              If you continue to have issues, please contact support or try registering again.
            </p>
          </div>
        )}
        
        {state.status === 'loading' && (
          <p className="text-sm text-gray-500">
            This may take a few moments...
          </p>
        )}
      </div>
    </div>
  );
}
