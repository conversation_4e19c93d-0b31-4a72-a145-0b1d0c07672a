import React, { useState } from 'react';
import { Mail, Lock, Eye, EyeOff, Play, User as UserIcon, Home, AlertCircle } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { auth } from '../../lib/supabase';
import { User, UserPreferences } from '../../types';
import { createDemoUser, getDemoVideos, getDemoPlaylists } from '../../utils/demoData';
import { secureStorage } from '../../utils/secureStorage';

interface LoginFormProps {
  onBackToHome?: () => void;
}

export function LoginForm({ onBackToHome }: LoginFormProps) {
  const { dispatch } = useApp();
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    openaiApiKey: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!isLogin) {
      if (!formData.name) {
        newErrors.name = 'Name is required';
      }
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsLoading(true);
    setErrors({});
    
    try {
      if (isLogin) {
        // Sign in existing user
        const { data, error } = await auth.signIn(formData.email, formData.password);
        
        if (error) {
          console.error('Sign in error:', error);
          setErrors({ general: error.message || 'Failed to sign in. Please check your credentials.' });
          return;
        }
        
        // User data will be loaded automatically by the auth state change listener
      } else {
        // Validate API key if provided
        if (formData.openaiApiKey) {
          const validation = secureStorage.validateApiKey(formData.openaiApiKey);
          if (!validation.valid) {
            setErrors({ openaiApiKey: validation.error || 'Invalid API key' });
            return;
          }
        }

        // Sign up new user (without API key in metadata)
        const { data, error } = await auth.signUp(
          formData.email,
          formData.password,
          formData.name
        );

        if (error) {
          console.error('Sign up error:', error);
          setErrors({ general: error.message || 'Failed to create account. Please try again.' });
          return;
        }

        if (data.user && !data.session) {
          // Email confirmation required
          // Store API key temporarily in localStorage for after confirmation
          if (formData.openaiApiKey) {
            localStorage.setItem('pending_openai_key', formData.openaiApiKey);
          }

          setErrors({
            general: 'Account created! Please check your email and click the confirmation link to complete registration.'
          });
          return;
        }

        // If user is immediately signed in, store API key securely
        if (data.session && formData.openaiApiKey) {
          const storeResult = await secureStorage.storeApiKey(formData.openaiApiKey);
          if (!storeResult.success) {
            console.warn('Failed to store API key:', storeResult.error);
          }
        }

        // User will be automatically signed in and data loaded
      }
    } catch (error: any) {
      console.error('Authentication error:', error);
      setErrors({ general: 'Network error. Please check your connection and try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    setTimeout(() => {
      // Create user with demo data
      const mockUser = createDemoUser('<EMAIL>', 'Demo User');
      dispatch({ type: 'SET_USER', payload: mockUser });
      
      // Load demo videos and playlists
      const demoVideos = getDemoVideos();
      const demoPlaylists = getDemoPlaylists();
      
      demoVideos.forEach(video => {
        dispatch({ type: 'ADD_VIDEO', payload: video });
      });
      
      demoPlaylists.forEach(playlist => {
        dispatch({ type: 'ADD_PLAYLIST', payload: playlist });
      });
      
      setIsLoading(false);
    }, 500);
  };


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md">
        {/* Back to Home Button */}
        {onBackToHome && (
          <div className="mb-4">
            <button
              onClick={onBackToHome}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              <Home className="h-4 w-4" />
              Back to Home
            </button>
          </div>
        )}

        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">AmILearning</h1>
          <p className="text-gray-600">Your distraction-free learning companion</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {errors.general && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{errors.general}</span>
            </div>
          )}

          {!isLogin && (
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Full Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.name ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="Enter your full name"
                required={!isLogin}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>
          )}

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="Enter your email"
                required
              />
            </div>
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email}</p>
            )}
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className={`w-full pl-10 pr-10 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.password ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
            {errors.password && (
              <p className="mt-1 text-sm text-red-600">{errors.password}</p>
            )}
          </div>

          {!isLogin && (
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                Confirm Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.confirmPassword ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="Confirm your password"
                  required={!isLogin}
                />
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>
          )}

          {!isLogin && (
            <div>
              <label htmlFor="openaiApiKey" className="block text-sm font-medium text-gray-700 mb-2">
                OpenAI API Key (Optional)
              </label>
              <input
                type="password"
                id="openaiApiKey"
                name="openaiApiKey"
                value={formData.openaiApiKey}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="sk-..."
              />
              <p className="mt-1 text-xs text-gray-500">
                Add your OpenAI API key to enable AI summaries and quizzes. Recommended: GPT-3.5-turbo (~$0.002/1K tokens). You can add this later in settings.
              </p>
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors duration-200 font-medium flex items-center justify-center"
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              isLogin ? 'Sign In' : 'Create Account'
            )}
          </button>
        </form>

        {/* Demo Login Section */}
        <form onSubmit={handleDemoSubmit} className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
          <div className="text-center mb-4">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Play className="h-5 w-5 text-blue-600" />
              <span className="font-semibold text-blue-800">Try Demo Mode</span>
            </div>
            <p className="text-sm text-blue-700">
              Experience AmILearning with sample data and features
            </p>
          </div>
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-2 px-4 rounded-lg hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <>
                <UserIcon className="h-4 w-4" />
                Enter Demo Mode
              </>
            )}
          </button>
        </form>
        
        <div className="mt-6 text-center">
          <button
            onClick={() => setIsLogin(!isLogin)}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            {isLogin ? "Don't have an account? Sign up" : 'Already have an account? Sign in'}
          </button>
          
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 mb-2">
              Demo includes sample videos, progress tracking, and all features. Real accounts start fresh.
            </p>
            <div className="flex items-center justify-center gap-4 text-xs text-gray-400">
              <span>✓ 5 Sample Videos</span>
              <span>✓ Progress Data</span>
              <span>✓ Achievements</span>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              💡 Recommended: Use GPT-3.5-turbo for cost-effective AI features (~$0.002/1K tokens)
            </p>
            <p className="text-xs text-gray-500 mt-1">
              🔒 Real accounts: Secure cloud storage, sync across devices, persistent data
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}