import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Play, Pause, Volume2, VolumeX, Maximize, BookOpen, Brain, Award, AlertTriangle, CheckCircle, XCircle, RotateCcw } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { databaseService } from '../../services/database';
import { calculateProgress } from '../../utils/videoUtils';
import { calculateQuizScore } from '../../utils/aiUtils';
import { UserAnswer, Question as QuestionType, Quiz } from '../../types';

export function VideoPlayer() {
  const { state, dispatch } = useApp();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [showSummary, setShowSummary] = useState(false);
  const [showQuiz, setShowQuiz] = useState(false);

  const video = state.currentVideo;
  const [videoError, setVideoError] = useState<string | null>(null);
  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, number>>({}); // Stores { questionId: selectedOptionIndex }
  const [quizSubmitted, setQuizSubmitted] = useState(false);
  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null | undefined>(null); // Holds the quiz from video state

  // Initialize player and quiz state when video changes
  useEffect(() => {
    if (video && videoRef.current) {
      setVideoError(null);
      // Load saved progress
      videoRef.current.currentTime = video.progress.watchTime || 0;
      setCurrentTime(video.progress.watchTime || 0);
      // Autoplay can be an option, for now, require manual play
      // setIsPlaying(true);
    }
    // Initialize quiz state from video object
    if (video?.quiz) {
      setCurrentQuiz(video.quiz);
      const initialAnswers: Record<string, number> = {};
      if (video.quiz.userAnswers && video.quiz.userAnswers.length > 0) {
        video.quiz.userAnswers.forEach(ua => {
          initialAnswers[ua.questionId] = ua.selectedAnswer;
        });
        // If userAnswers exist, it implies the quiz was submitted before (or at least attempted)
        setQuizSubmitted(!!video.quiz.completedAt);
      } else {
        // No user answers, quiz is fresh or reset
        setQuizSubmitted(false);
      }
      setSelectedAnswers(initialAnswers);
    } else {
      // No quiz for this video or video changed to one without a quiz
      setCurrentQuiz(null);
      setSelectedAnswers({});
      setQuizSubmitted(false);
    }
  }, [video]);

  // HTML5 Video event listeners
  useEffect(() => {
    const player = videoRef.current;
    if (!player) return;

    const handleLoadedMetadata = () => {
      setDuration(player.duration);
      // If there was a seek before metadata loaded, apply it now
      if (video?.progress.watchTime && video.progress.watchTime < player.duration) {
        player.currentTime = video.progress.watchTime;
      }
    };
    const handleTimeUpdate = () => {
      setCurrentTime(player.currentTime);
      if (video && duration > 0) { // ensure duration is loaded
        const updatedVideo = {
          ...video,
          progress: {
            ...video.progress,
            watchTime: player.currentTime,
            completionPercentage: calculateProgress(player.currentTime, player.duration),
            lastWatched: new Date(),
            status: calculateProgress(player.currentTime, player.duration) >= 95 ? 'completed' as const : 'in_progress' as const,
          },
        };
        
        // Update in database for authenticated users
        if (state.isAuthenticated) {
          databaseService.updateVideo(updatedVideo).catch(error => {
            console.error('Error updating video progress:', error);
          });
        }
        
        dispatch({ type: 'UPDATE_VIDEO', payload: updatedVideo });
      }
    };
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
      setIsPlaying(false);
      // Mark as completed if not already
      if (video && duration > 0) {
         const updatedVideo = {
          ...video,
          progress: { ...video.progress, watchTime: duration, completionPercentage: 100, status: 'completed' as const },
        };
        dispatch({ type: 'UPDATE_VIDEO', payload: updatedVideo });
      }
    };
    const handleVolumeChange = () => {
      setVolume(player.volume);
      setIsMuted(player.muted);
    };
    const handleError = () => {
      console.error("Video Error:", player.error);
      setVideoError(player.error?.message || "An unknown video error occurred.");
    };

    player.addEventListener('loadedmetadata', handleLoadedMetadata);
    player.addEventListener('timeupdate', handleTimeUpdate);
    player.addEventListener('play', handlePlay);
    player.addEventListener('pause', handlePause);
    player.addEventListener('ended', handleEnded);
    player.addEventListener('volumechange', handleVolumeChange);
    player.addEventListener('error', handleError);

    return () => {
      player.removeEventListener('loadedmetadata', handleLoadedMetadata);
      player.removeEventListener('timeupdate', handleTimeUpdate);
      player.removeEventListener('play', handlePlay);
      player.removeEventListener('pause', handlePause);
      player.removeEventListener('ended', handleEnded);
      player.removeEventListener('volumechange', handleVolumeChange);
      player.removeEventListener('error', handleError);
    };
  }, [video, dispatch, duration]);


  const handleBack = () => {
    dispatch({ type: 'SET_CURRENT_VIDEO', payload: null });
  };

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play().catch(err => {
          console.error("Play failed:", err);
          setVideoError("Playback failed. Please try again or check browser permissions.");
        });
      }
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (videoRef.current) {
      const newTime = Number(e.target.value);
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime); // Optimistically update UI
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (videoRef.current) {
      const newVolume = Number(e.target.value);
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      videoRef.current.muted = newVolume === 0;
      setIsMuted(newVolume === 0);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
      // If unmuting and volume was 0, set to a default volume
      if (!videoRef.current.muted && videoRef.current.volume === 0) {
        videoRef.current.volume = 0.5;
        setVolume(0.5);
      }
    }
  };

  const toggleFullScreen = () => {
    if (videoRef.current?.parentElement) { // Request fullscreen on the container for custom controls
      if (!document.fullscreenElement) {
        videoRef.current.parentElement.requestFullscreen().catch(err => {
          alert(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
        });
      } else {
        document.exitFullscreen();
      }
    }
  };


  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const completionPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  const handleAnswerSelect = (questionId: string, optionIndex: number) => {
    if (quizSubmitted) return; // Don't allow changes after submission

    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: optionIndex,
    }));
  };

  const handleSubmitQuiz = () => {
    if (!currentQuiz || !video) return;

    const userAnswers: UserAnswer[] = currentQuiz.questions.map(q => {
      const selectedOption = selectedAnswers[q.id];
      return {
        questionId: q.id,
        selectedAnswer: selectedOption,
        isCorrect: selectedOption === q.correctAnswer,
      };
    });

    const updatedQuiz: Quiz = {
      ...currentQuiz,
      userAnswers: userAnswers,
      score: calculateQuizScore({ ...currentQuiz, userAnswers }), // Pass updated userAnswers to calculate score
      completedAt: new Date(),
    };

    setCurrentQuiz(updatedQuiz); // Update local state for immediate UI feedback
    setQuizSubmitted(true);

    // Update the video object in the global state
    dispatch({
      type: 'UPDATE_VIDEO',
      payload: { ...video, quiz: updatedQuiz },
    });

    // Award points for completing a quiz (example)
    if (state.user) {
      dispatch({
        type: 'SET_USER',
        payload: { ...state.user, points: state.user.points + (updatedQuiz.score || 0) }
      });
    }
  };

  const handleRetakeQuiz = () => {
    if (!currentQuiz || !video) return;

    // Create a new quiz object instance without userAnswers and score to reset it
    const resetQuizData: Quiz = {
        id: currentQuiz.id, // Retain the same quiz ID
        questions: currentQuiz.questions.map(q => ({ ...q })), // Deep copy questions
        // userAnswers, score, and completedAt are omitted to reset them
    };

    setCurrentQuiz(resetQuizData);
    setSelectedAnswers({});
    setQuizSubmitted(false);

    // Update the video object in the global state with the reset quiz
    dispatch({
      type: 'UPDATE_VIDEO',
      payload: { ...video, quiz: resetQuizData },
    });
  };


  if (!video) return null;

  const allQuestionsAnswered = currentQuiz?.questions.every(q => selectedAnswers[q.id] !== undefined);

  return (
    <div className={`
      min-h-screen flex flex-col
      ${state.user?.preferences.darkMode ? 'dark:bg-gray-900' : 'bg-gray-50'}
      ${state.user?.preferences.focusMode ? 'bg-black dark:bg-black' : ''}
    `}>
      {/* Header */}
      {!state.user?.preferences.focusMode && (
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>
            <div className="flex-1">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {video.title}
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {completionPercentage.toFixed(0)}% completed
              </p>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setShowSummary(!showSummary)}
                className={`
                  p-2 rounded-lg transition-colors duration-200 flex items-center gap-2
                  ${showSummary 
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300' 
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400'
                  }
                `}
              >
                <BookOpen className="h-5 w-5" />
                Summary
              </button>
              <button
                onClick={() => setShowQuiz(!showQuiz)}
                className={`
                  p-2 rounded-lg transition-colors duration-200 flex items-center gap-2
                  ${showQuiz 
                    ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' 
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400'
                  }
                `}
              >
                <Brain className="h-5 w-5" />
                Quiz
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex-1 flex">
        {/* Video Player */}
        <div className="flex-1 flex flex-col">
          {/* Video Container */}
          <div className="flex-1 bg-black flex items-center justify-center relative group">
            {/* Actual Video Player */}
            <video
              ref={videoRef}
              src={video.url} // Assuming video.url is a direct link to a video file or a supported source
              poster={video.thumbnail}
              className="w-full h-full max-w-5xl max-h-[calc(100vh-180px)] object-contain" // Adjusted max-h for controls
              onClick={togglePlay} // Play/pause on video click
              onDoubleClick={toggleFullScreen} // Fullscreen on double click
            />

            {videoError && (
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-80 text-white p-4">
                <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
                <h3 className="text-xl font-semibold mb-2">Video Playback Error</h3>
                <p className="text-center mb-4">{videoError}</p>
                <p className="text-sm text-gray-400">If this is a YouTube/Vimeo link, direct playback might be restricted. Ensure the URL is a direct video file link or use a platform-specific player.</p>
              </div>
            )}

            {/* Play/Pause Overlay (shows on hover when paused) */}
            {!isPlaying && !videoError && (
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                <button
                  onClick={togglePlay}
                  className="bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full p-4 transition-all duration-200 pointer-events-auto"
                >
                  {isPlaying ? (
                    <Pause className="h-12 w-12 text-white" />
                  ) : (
                    <Play className="h-12 w-12 text-white ml-1" />
                  )}
                </button>
              </div>
            )}

              {/* Progress indicator (simplified, as main progress is in controls) */}
              {duration > 0 && (
                 <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white bg-opacity-20 rounded-full h-1 cursor-pointer" onClick={(e) => {
                        // Allow clicking on this simple bar to seek
                        if (videoRef.current) {
                            const rect = e.currentTarget.getBoundingClientRect();
                            const clickX = e.clientX - rect.left;
                            const newTime = (clickX / rect.width) * duration;
                            videoRef.current.currentTime = newTime;
                            setCurrentTime(newTime);
                        }
                    }}>
                    <div
                        className="bg-blue-500 h-1 rounded-full transition-all duration-100" // Faster update for direct manipulation
                        style={{ width: `${completionPercentage}%` }}
                    />
                    </div>
                </div>
              )}
          </div>

          {/* Controls */}
          {!state.user?.preferences.focusMode && (
            <div className="bg-white dark:bg-gray-800 p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="max-w-5xl mx-auto space-y-3">
                {/* Progress Bar */}
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600 dark:text-gray-400 min-w-0">
                    {formatTime(currentTime)}
                  </span>
                  <input
                    type="range"
                    min="0"
                    max={duration}
                    value={currentTime}
                    onChange={handleSeek}
                    disabled={!duration || videoError !== null}
                    className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{
                      background: duration > 0 ? `linear-gradient(to right, #3b82f6 ${completionPercentage}%, rgb(209 213 219 / var(--tw-bg-opacity)) ${completionPercentage}%)` : 'rgb(209 213 219 / var(--tw-bg-opacity))'
                    }}
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400 min-w-0">
                    {formatTime(duration)}
                  </span>
                </div>

                {/* Control Buttons */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <button
                      onClick={togglePlay}
                      disabled={videoError !== null}
                      className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isPlaying ? (
                        <Pause className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                      ) : (
                        <Play className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                      )}
                    </button>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={toggleMute}
                        disabled={videoError !== null}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isMuted ? (
                          <VolumeX className="h-5 w-5 text-gray-700 dark:text-gray-300" />
                        ) : (
                          <Volume2 className="h-5 w-5 text-gray-700 dark:text-gray-300" />
                        )}
                      </button>
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.05"
                        value={isMuted ? 0 : volume}
                        onChange={handleVolumeChange}
                        disabled={videoError !== null}
                        className="w-20 h-1 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                        style={{
                          background: !isMuted ? `linear-gradient(to right, #4f46e5 ${volume * 100}%, rgb(209 213 219 / var(--tw-bg-opacity)) ${volume * 100}%)` : 'rgb(209 213 219 / var(--tw-bg-opacity))'
                        }}
                      />
                    </div>
                  </div>

                  <button
                    onClick={toggleFullScreen}
                    disabled={videoError !== null}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Maximize className="h-5 w-5 text-gray-700 dark:text-gray-300" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Side Panel */}
        {!state.user?.preferences.focusMode && (showSummary || showQuiz) && (
          <div className="w-96 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
            {showSummary && video.summary && (
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Adaptive Summary
                </h3>
                <div className="space-y-4">
                  <p className="text-gray-700 dark:text-gray-300">
                    {video.summary.content}
                  </p>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                      Key Points:
                    </h4>
                    <ul className="space-y-2">
                      {video.summary.keyPoints.map((point, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300">{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {showQuiz && currentQuiz && (
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Personalized Quiz
                  </h3>
                  {quizSubmitted && currentQuiz.score !== undefined && (
                     <span className={`font-semibold px-2 py-1 rounded-md text-sm ${
                        currentQuiz.score >= 70 ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                                             : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                      }`}>
                       Score: {currentQuiz.score}%
                     </span>
                  )}
                </div>

                <div className="space-y-6">
                  {currentQuiz.questions.map((question: QuestionType, index: number) => {
                    const userAnswer = quizSubmitted ? currentQuiz.userAnswers?.find(ua => ua.questionId === question.id) : undefined;
                    const selectedOptionIndex = selectedAnswers[question.id];

                    return (
                      <div key={question.id} className="space-y-3">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {index + 1}. {question.question}
                        </h4>
                        <div className="space-y-2">
                          {question.options.map((option, optionIndex) => {
                            let buttonClass = "w-full text-left p-3 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200";
                            let icon = null;

                            if (quizSubmitted && userAnswer) {
                              const isSelected = userAnswer.selectedAnswer === optionIndex;
                              const isCorrect = question.correctAnswer === optionIndex;

                              if (isCorrect) {
                                buttonClass += " bg-green-50 dark:bg-green-900/30 border-green-500 dark:border-green-600";
                                icon = <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 ml-auto" />;
                              } else if (isSelected && !isCorrect) {
                                buttonClass += " bg-red-50 dark:bg-red-900/30 border-red-500 dark:border-red-600";
                                icon = <XCircle className="h-5 w-5 text-red-600 dark:text-red-400 ml-auto" />;
                              } else {
                                buttonClass += " opacity-70"; // Fade out non-selected, non-correct answers
                              }
                            } else if (selectedAnswers[question.id] === optionIndex) {
                              buttonClass += " bg-blue-50 dark:bg-blue-900/30 border-blue-500 dark:border-blue-600";
                            }

                            return (
                              <button
                                key={optionIndex}
                                onClick={() => handleAnswerSelect(question.id, optionIndex)}
                                disabled={quizSubmitted}
                                className={`${buttonClass} flex items-center`}
                              >
                                <span className="flex-1">{option}</span>
                                {icon}
                              </button>
                            );
                          })}
                        </div>
                        {quizSubmitted && userAnswer && (
                          <div className={`mt-2 p-2 rounded-md text-sm ${userAnswer.isCorrect ? 'bg-green-50 dark:bg-green-800/50 text-green-700 dark:text-green-300' : 'bg-red-50 dark:bg-red-800/50 text-red-700 dark:text-red-300'}`}>
                            {userAnswer.isCorrect ? 'Correct! ' : 'Incorrect. '}
                            {question.explanation}
                          </div>
                        )}
                      </div>
                    );
                  })}
                  
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    {!quizSubmitted ? (
                      <button
                        onClick={handleSubmitQuiz}
                        disabled={!allQuestionsAnswered}
                        className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center gap-2 disabled:bg-gray-400 dark:disabled:bg-gray-600 disabled:cursor-not-allowed"
                      >
                        <Award className="h-4 w-4" />
                        Submit Quiz
                      </button>
                    ) : (
                      <button
                        onClick={handleRetakeQuiz}
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center gap-2"
                      >
                        <RotateCcw className="h-4 w-4" />
                        Retake Quiz
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}