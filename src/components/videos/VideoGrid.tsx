import React, { useState } from 'react';
import { Play, Clock, CheckCircle, MoreVertical, Tag, Calendar, Trash2, Edit3, Video } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { databaseService } from '../../services/database';
import { getProgressStatus, formatDuration } from '../../utils/videoUtils';

export function VideoGrid() {
  const { state, dispatch } = useApp();
  const [filterTag, setFilterTag] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'title' | 'progress'>('recent');
  const [showDropdown, setShowDropdown] = useState<string | null>(null);

  // Get all unique tags
  const allTags = Array.from(new Set(state.videos.flatMap(video => video.tags)));

  // Filter and sort videos
  const filteredVideos = state.videos
    .filter(video => !filterTag || video.tags.includes(filterTag))
    .sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'progress':
          return b.progress.completionPercentage - a.progress.completionPercentage;
        case 'recent':
        default:
          return new Date(b.progress.lastWatched).getTime() - new Date(a.progress.lastWatched).getTime();
      }
    });

  const handleVideoClick = (video: any) => {
    dispatch({ type: 'SET_CURRENT_VIDEO', payload: video });
  };

  const handleDeleteVideo = (videoId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this video?')) {
      if (state.isAuthenticated) {
        databaseService.deleteVideo(videoId).then(() => {
          dispatch({ type: 'DELETE_VIDEO', payload: videoId });
        }).catch(error => {
          console.error('Error deleting video:', error);
          alert('Failed to delete video. Please try again.');
        });
      } else {
        dispatch({ type: 'DELETE_VIDEO', payload: videoId });
      }
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <Play className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'in_progress':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-300';
    }
  };

  return (
    <div className={`
      p-6 space-y-6
      ${state.user?.preferences.darkMode ? 'dark:bg-gray-900' : 'bg-gray-50'}
      min-h-screen
    `}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Videos</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {filteredVideos.length} video{filteredVideos.length !== 1 ? 's' : ''}
          </p>
        </div>

        {/* Filters and Sort */}
        <div className="flex flex-col sm:flex-row gap-3">
          <select
            value={filterTag}
            onChange={(e) => setFilterTag(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Tags</option>
            {allTags.map(tag => (
              <option key={tag} value={tag}>{tag}</option>
            ))}
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="recent">Most Recent</option>
            <option value="title">Title A-Z</option>
            <option value="progress">Progress</option>
          </select>
        </div>
      </div>

      {/* Videos Grid */}
      {filteredVideos.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredVideos.map((video) => {
            const status = getProgressStatus(video.progress.completionPercentage);
            
            return (
              <div
                key={video.id}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 cursor-pointer group overflow-hidden"
                onClick={() => handleVideoClick(video)}
              >
                {/* Thumbnail */}
                <div className="relative">
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    className="w-full h-48 object-cover"
                    onError={(e) => {
                      e.currentTarget.src = 'https://images.pexels.com/photos/5428836/pexels-photo-5428836.jpeg?auto=compress&cs=tinysrgb&w=320&h=180&dpr=1';
                    }}
                  />
                  
                  {/* Play overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-200 flex items-center justify-center">
                    <Play className="h-12 w-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </div>

                  {/* Progress bar */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700">
                    <div
                      className={`h-full ${getStatusColor(status)} transition-all duration-300`}
                      style={{ width: `${video.progress.completionPercentage}%` }}
                    />
                  </div>

                  {/* Status badge */}
                  <div className="absolute top-3 left-3">
                    {getStatusIcon(status)}
                  </div>

                  {/* More options */}
                  <div className="absolute top-3 right-3">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowDropdown(showDropdown === video.id ? null : video.id);
                      }}
                      className="p-1 rounded-full bg-black bg-opacity-50 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </button>

                    {showDropdown === video.id && (
                      <div className="absolute right-0 top-8 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 py-1 z-10">
                        <button
                          onClick={(e) => handleDeleteVideo(video.id, e)}
                          className="w-full px-3 py-2 text-left text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center gap-2 text-sm"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Content */}
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 dark:text-white text-lg mb-2 line-clamp-2">
                    {video.title}
                  </h3>
                  
                  {video.description && (
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                      {video.description}
                    </p>
                  )}

                  {/* Tags */}
                  {video.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {video.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full"
                        >
                          <Tag className="h-3 w-3" />
                          {tag}
                        </span>
                      ))}
                      {video.tags.length > 3 && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          +{video.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  )}

                  {/* Meta info */}
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {new Date(video.addedAt).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {video.duration > 0 ? formatDuration(video.duration) : 'Unknown'}
                    </div>
                  </div>

                  {/* Progress text */}
                  <div className="mt-2 text-sm">
                    {status === 'completed' && (
                      <span className="text-green-600 dark:text-green-400 font-medium">Completed</span>
                    )}
                    {status === 'in_progress' && (
                      <span className="text-yellow-600 dark:text-yellow-400 font-medium">
                        {video.progress.completionPercentage}% watched
                      </span>
                    )}
                    {status === 'not_started' && (
                      <span className="text-gray-500 dark:text-gray-400">Not started</span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md mx-auto">
            <Video className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No videos found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {filterTag 
                ? `No videos found with the tag "${filterTag}"`
                : "Add your first video to get started with your learning journey!"
              }
            </p>
            {filterTag && (
              <button
                onClick={() => setFilterTag('')}
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                Clear filter
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}