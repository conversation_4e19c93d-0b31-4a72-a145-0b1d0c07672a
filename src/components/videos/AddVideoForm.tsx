import React, { useState } from 'react';
import { Link, Plus, X, Loader } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { databaseService } from '../../services/database';
import { Video } from '../../types';
import { extractVideoId, getVideoThumbnail } from '../../utils/videoUtils';
import { generateVideoSummary, generateQuiz } from '../../utils/aiUtils';

interface AddVideoFormProps {
  onClose: () => void;
}

export function AddVideoForm({ onClose }: AddVideoFormProps) {
  const { state, dispatch } = useApp();
  const [formData, setFormData] = useState({
    url: '',
    title: '',
    description: '',
    tags: '',
    playlist: '',
  });
  const [isGenerating, setIsGenerating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsGenerating(true);

    try {
      const videoId = extractVideoId(formData.url);
      if (!videoId) {
        alert('Please enter a valid YouTube or video URL');
        return;
      }

      const newVideo: Video = {
        id: crypto.randomUUID(),
        url: formData.url,
        title: formData.title || 'Untitled Video',
        description: formData.description,
        thumbnail: getVideoThumbnail(formData.url),
        duration: 0, // Would be fetched from API in production
        addedAt: new Date(),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        playlist: formData.playlist || undefined,
        progress: {
          status: 'not_started',
          watchTime: 0,
          lastWatched: new Date(),
          completionPercentage: 0,
        },
      };

      // Generate AI summary and quiz
      const [summary, quiz] = await Promise.all([
        generateVideoSummary(formData.url, formData.title),
        generateQuiz(formData.url, formData.title),
      ]);

      newVideo.summary = summary;
      newVideo.quiz = quiz;

      if (state.isAuthenticated) {
        // Save to database for authenticated users
        const savedVideo = await databaseService.addVideo(newVideo);
        dispatch({ type: 'ADD_VIDEO', payload: savedVideo });
      } else {
        // Save to localStorage for demo users
        dispatch({ type: 'ADD_VIDEO', payload: newVideo });
      }
      
      // Award points for adding video
      if (state.user) {
        const updatedUser = {
          ...state.user,
          points: state.user.points + 10,
        };
        
        if (state.isAuthenticated) {
          await databaseService.updateUserProfile(updatedUser);
        }
        
        dispatch({ type: 'SET_USER', payload: updatedUser });
      }

      onClose();
    } catch (error) {
      console.error('Error adding video:', error);
      alert('Failed to add video. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Add New Video
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Video URL *
            </label>
            <div className="relative">
              <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="url"
                id="url"
                name="url"
                value={formData.url}
                onChange={handleInputChange}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="https://youtube.com/watch?v=..."
                required
              />
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Supports YouTube, Vimeo, and other video platforms
            </p>
          </div>

          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Video Title
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter video title"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Brief description of the video content"
            />
          </div>

          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tags
            </label>
            <input
              type="text"
              id="tags"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="programming, tutorial, javascript (comma-separated)"
            />
          </div>

          <div>
            <label htmlFor="playlist" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Playlist (Optional)
            </label>
            <select
              id="playlist"
              name="playlist"
              value={formData.playlist}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="">Select playlist</option>
              {state.playlists.map(playlist => (
                <option key={playlist.id} value={playlist.id}>
                  {playlist.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center gap-4 pt-4">
            <button
              type="submit"
              disabled={isGenerating}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors duration-200 font-medium flex items-center justify-center gap-2"
            >
              {isGenerating ? (
                <>
                  <Loader className="h-4 w-4 animate-spin" />
                  Generating Summary & Quiz...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  Add Video
                </>
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </form>

        {isGenerating && (
          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              🧠 Analyzing content and creating personalized summary and quiz questions adapted to your learning journey. This may take a moment...
            </p>
          </div>
        )}
      </div>
    </div>
  );
}