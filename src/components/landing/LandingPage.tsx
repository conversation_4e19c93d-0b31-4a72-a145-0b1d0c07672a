import React from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  Target, 
  Zap, 
  Star, 
  Users, 
  BookOpen, 
  Award,
  ArrowRight,
  CheckCircle,
  Sparkles,
  Heart,
  Coffee
} from 'lucide-react';

interface LandingPageProps {
  onGetStarted: () => void;
  onWatchDemo: () => void;
}

export function LandingPage({ onGetStarted, onWatchDemo }: LandingPageProps) {
  const features = [
    {
      icon: Brain,
      title: 'Adaptive Learning Path',
      description: 'Intelligent analysis adapts to your learning journey and suggests personalized paths',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Target,
      title: 'Progress Tracking',
      description: 'Visual progress tracking with smart analytics and insights',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Zap,
      title: 'Focus Mode',
      description: 'Distraction-free learning environment for maximum concentration',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Award,
      title: 'Gamification',
      description: 'Earn points, badges, and achievements as you learn',
      color: 'from-green-500 to-emerald-500'
    }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Computer Science Student',
      content: 'AmILearning transformed how I study. The AI summaries save me hours!',
      avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&dpr=1'
    },
    {
      name: 'Marcus Rodriguez',
      role: 'Software Developer',
      content: 'The focus mode is a game-changer. I can finally watch tutorials without distractions.',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&dpr=1'
    },
    {
      name: 'Emily Watson',
      role: 'UX Designer',
      content: 'Beautiful interface and the gamification keeps me motivated to learn every day.',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=64&h=64&dpr=1'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          {/* Large Floating Orbs */}
          <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full animate-pulse blur-3xl"></div>
          <div className="absolute top-40 right-32 w-48 h-48 bg-gradient-to-r from-blue-400/15 to-cyan-400/15 rounded-full animate-pulse delay-1000 blur-2xl"></div>
          <div className="absolute bottom-32 left-32 w-80 h-80 bg-gradient-to-r from-green-400/10 to-blue-400/10 rounded-full animate-pulse delay-2000 blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-56 h-56 bg-gradient-to-r from-yellow-400/15 to-orange-400/15 rounded-full animate-pulse delay-3000 blur-2xl"></div>
          
          {/* Moving Particles */}
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white/30 rounded-full animate-ping"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${2 + Math.random() * 4}s`
              }}
            />
          ))}
          
          {/* Gradient Waves */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-900/5 to-transparent animate-pulse"></div>
          <div className="absolute inset-0 bg-gradient-to-l from-transparent via-blue-900/5 to-transparent animate-pulse delay-1000"></div>
        </div>
        
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http://www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%239C92AC%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-32">
          <div className="text-center">
            {/* Logo */}
            <div className="flex items-center justify-center mb-8">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-2xl">
                  <Brain className="h-8 w-8 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                  <Sparkles className="h-3 w-3 text-white" />
                </div>
              </div>
              <h1 className="ml-4 text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                AmILearning
              </h1>
            </div>

            {/* Hero Text */}
            <h2 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              Learn Smarter,
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                Not Harder
              </span>
            </h2>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Transform any video into an interactive learning experience with intelligent analysis that adapts to your journey, 
              personalized suggestions, and distraction-free focus modes.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
              <button
                onClick={onGetStarted}
                className="group relative px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-2xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-105"
              >
                <span className="flex items-center gap-2">
                  Start Learning Free
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </span>
              </button>
              
              <button
                onClick={onWatchDemo}
                className="px-8 py-4 border-2 border-white/20 text-white font-semibold rounded-2xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm flex items-center gap-2"
              >
                <Play className="h-5 w-5" />
                Watch Demo
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Brain className="h-8 w-8 text-white" />
                </div>
                <div className="text-xl font-bold text-white mb-2">AI-Powered Analysis</div>
                <div className="text-gray-300">Intelligent content analysis that adapts to your learning style</div>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <div className="text-xl font-bold text-white mb-2">Personalized Learning</div>
                <div className="text-gray-300">Customized summaries and quizzes tailored to your progress</div>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <div className="text-xl font-bold text-white mb-2">Distraction-Free</div>
                <div className="text-gray-300">Focus mode eliminates distractions for better concentration</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 bg-white/5 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-4xl font-bold text-white mb-4">
              Supercharge Your Learning
            </h3>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Discover features designed to make learning more effective, engaging, and enjoyable
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className="group relative p-8 bg-white/10 backdrop-blur-sm rounded-3xl border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <h4 className="text-xl font-semibold text-white mb-3">
                    {feature.title}
                  </h4>
                  <p className="text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Testimonials */}
      <div className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-4xl font-bold text-white mb-4">
              Loved by Learners Worldwide
            </h3>
            <p className="text-xl text-gray-300">
              See what our community has to say
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="p-8 bg-white/10 backdrop-blur-sm rounded-3xl border border-white/20 hover:bg-white/20 transition-all duration-300"
              >
                <div className="flex items-center mb-6">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full mr-4"
                  />
                  <div>
                    <div className="font-semibold text-white">{testimonial.name}</div>
                    <div className="text-gray-400 text-sm">{testimonial.role}</div>
                  </div>
                </div>
                <p className="text-gray-300 leading-relaxed">
                  "{testimonial.content}"
                </p>
                <div className="flex text-yellow-400 mt-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-current" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="py-24 bg-white/5 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-4xl font-bold text-white mb-4">
            Start Your Learning Journey
          </h3>
          <p className="text-xl text-gray-300 mb-12">
            Begin with our core features and expand your learning experience as you grow.
          </p>

          <div className="bg-white/10 backdrop-blur-sm rounded-3xl border border-white/20 p-8 max-w-md mx-auto">
            <div className="text-center mb-8">
              <div className="text-5xl font-bold text-white mb-2">Start</div>
              <div className="text-gray-300">Learning Today</div>
            </div>

            <ul className="space-y-4 mb-8">
              {[
                'Add your learning videos',
                'Adaptive content analysis',
                'Interactive quizzes',
                'Progress tracking',
                'Achievement system'
              ].map((feature, index) => (
                <li key={index} className="flex items-center text-gray-300">
                  <CheckCircle className="h-5 w-5 text-green-400 mr-3" />
                  {feature}
                </li>
              ))}
            </ul>

            <button
              onClick={onGetStarted}
              className="w-full py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-2xl hover:shadow-lg transition-all duration-300 transform hover:scale-105"
            >
              Get Started Now
            </button>

            <div className="mt-6 flex items-center justify-center text-gray-400">
              <Heart className="h-4 w-4 mr-2 text-red-400" />
              <span className="text-sm">
                Support our mission to make learning accessible for everyone
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="py-12 border-t border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                <Brain className="h-4 w-4 text-white" />
              </div>
              <span className="text-white font-semibold">AmILearning</span>
            </div>
            
            <div className="flex items-center space-x-6 text-gray-400">
              <a href="#" className="hover:text-white transition-colors">Privacy</a>
              <a href="#" className="hover:text-white transition-colors">Terms</a>
              <a href="#" className="hover:text-white transition-colors">Support</a>
              <div className="flex items-center">
                <Coffee className="h-4 w-4 mr-1" />
                <span className="text-sm">Made with love</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}