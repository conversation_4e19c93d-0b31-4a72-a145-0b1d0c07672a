import React from 'react';
import { Play, Clock, CheckCircle, TrendingUp, Award, Target, Zap, BookOpen, Brain, Star, Calendar, Users, Sparkles, ArrowRight, Trophy, Siren as Fire, LogOut } from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { getProgressStatus } from '../../utils/videoUtils';

export function Dashboard() {
  const { state, dispatch } = useApp();

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to sign out?')) {
      dispatch({ type: 'LOGOUT' });
    }
  };

  const stats = {
    totalVideos: state.videos.length,
    completedVideos: state.videos.filter(v => getProgressStatus(v.progress.completionPercentage) === 'completed').length,
    inProgressVideos: state.videos.filter(v => getProgressStatus(v.progress.completionPercentage) === 'in_progress').length,
    totalWatchTime: state.videos.reduce((acc, v) => acc + v.progress.watchTime, 0),
  };

  const recentVideos = state.videos
    .sort((a, b) => new Date(b.progress.lastWatched).getTime() - new Date(a.progress.lastWatched).getTime())
    .slice(0, 3);

  const achievements = [
    { 
      id: 1, 
      title: 'First Steps', 
      description: 'Added your first video', 
      earned: stats.totalVideos > 0,
      icon: Play,
      color: 'from-blue-500 to-cyan-500'
    },
    { 
      id: 2, 
      title: 'Knowledge Seeker', 
      description: 'Completed 5 videos', 
      earned: stats.completedVideos >= 5,
      icon: BookOpen,
      color: 'from-green-500 to-emerald-500'
    },
    { 
      id: 3, 
      title: 'Time Master', 
      description: 'Watched 10+ hours', 
      earned: stats.totalWatchTime >= 36000,
      icon: Clock,
      color: 'from-purple-500 to-pink-500'
    },
    { 
      id: 4, 
      title: 'Quiz Champion', 
      description: 'Scored 90%+ on 3 quizzes', 
      earned: false,
      icon: Brain,
      color: 'from-yellow-500 to-orange-500'
    },
  ];

  // const learningStreak = 7; // Mock data - Removed for MVP
  // const weeklyGoal = 5; // hours - Removed for MVP
  // const weeklyProgress = Math.min(stats.totalWatchTime / 3600, weeklyGoal); - Removed for MVP

  // Calculate total watch time in hours for display if needed elsewhere, or remove if only for weeklyGoal
  const totalWatchTimeHours = stats.totalWatchTime / 3600;


  return (
    <div className={`
      min-h-screen
      ${state.user?.preferences.darkMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900' 
        : 'bg-gradient-to-br from-blue-50 via-purple-50/30 to-indigo-50'
      }
    `}>
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http://www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%239C92AC%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        
        {/* Floating Elements */}
        <div className="absolute top-10 right-20 w-20 h-20 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 left-10 w-16 h-16 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full animate-pulse delay-1000"></div>

        <div className="relative p-8">
          <div className="max-w-7xl mx-auto">
            {/* Welcome Header */}
            <div className="mb-12 relative">
              {/* Logout Button */}
              <div className="absolute top-0 right-0">
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl font-medium"
                >
                  <LogOut className="h-4 w-4" />
                  Sign Out
                </button>
              </div>

              <div className="flex items-center gap-4 mb-6">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-2xl">
                    <span className="text-2xl font-bold text-white">
                      {state.user?.name?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                  {/* {learningStreak > 0 && ( // Removed for MVP
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-orange-400 to-red-400 rounded-full flex items-center justify-center">
                      <Fire className="h-4 w-4 text-white" />
                    </div>
                  )} */}
                </div>
                <div>
                  <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                    Welcome back, {state.user?.name}! 🚀
                  </h1>
                  <p className="text-xl text-gray-600 dark:text-gray-300">
                    Ready to continue your learning adventure?
                  </p>
                  {/* {learningStreak > 0 && ( // Removed for MVP
                    <div className="flex items-center gap-2 mt-2">
                      <Fire className="h-5 w-5 text-orange-500" />
                      <span className="text-orange-600 dark:text-orange-400 font-semibold">
                        {learningStreak} day learning streak!
                      </span>
                    </div>
                  )} */}
                </div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              <div className="group relative overflow-hidden bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-white/20">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center">
                      <Play className="h-6 w-6 text-white" />
                    </div>
                    <Sparkles className="h-5 w-5 text-blue-500 opacity-60" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Total Videos</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.totalVideos}</p>
                </div>
              </div>

              <div className="group relative overflow-hidden bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-white/20">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center">
                      <CheckCircle className="h-6 w-6 text-white" />
                    </div>
                    <Trophy className="h-5 w-5 text-green-500 opacity-60" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Completed</p>
                  <p className="text-3xl font-bold text-green-600">{stats.completedVideos}</p>
                </div>
              </div>

              <div className="group relative overflow-hidden bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-white/20">
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center">
                      <Clock className="h-6 w-6 text-white" />
                    </div>
                    <Zap className="h-5 w-5 text-yellow-500 opacity-60" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">In Progress</p>
                  <p className="text-3xl font-bold text-yellow-600">{stats.inProgressVideos}</p>
                </div>
              </div>

              <div className="group relative overflow-hidden bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-white/20">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                    <Star className="h-5 w-5 text-purple-500 opacity-60" />
                  </div>
                  <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">Learning Path</h3>
                  <p className="text-xs text-purple-600 dark:text-purple-400 leading-tight">
                    Add a video and let our intelligent system analyze and adapt content to your learning path
                  </p>
                </div>
              </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Continue Watching */}
              <div className="lg:col-span-2">
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-white/20">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Continue Learning
                    </h2>
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                  </div>
                  
                  {recentVideos.length > 0 ? (
                    <div className="space-y-4">
                      {recentVideos.map((video) => (
                        <div key={video.id} className="group flex items-center gap-4 p-4 bg-gray-50/50 dark:bg-gray-700/50 rounded-2xl hover:bg-gray-100/50 dark:hover:bg-gray-600/50 transition-all duration-200 cursor-pointer">
                          <div className="relative">
                            <img
                              src={video.thumbnail}
                              alt={video.title}
                              className="w-20 h-14 object-cover rounded-xl"
                              onError={(e) => {
                                e.currentTarget.src = 'https://images.pexels.com/photos/5428836/pexels-photo-5428836.jpeg?auto=compress&cs=tinysrgb&w=80&h=56&dpr=1';
                              }}
                            />
                            <div className="absolute inset-0 bg-black/20 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                              <Play className="h-6 w-6 text-white" />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900 dark:text-white truncate mb-1">
                              {video.title}
                            </h3>
                            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2">
                              <div
                                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${video.progress.completionPercentage}%` }}
                              />
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {video.progress.completionPercentage}% complete
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <BookOpen className="h-8 w-8 text-gray-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        Start Your Learning Journey
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Add your first video to begin tracking your progress
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Weekly Progress & Achievements */}
              <div className="space-y-8">
                {/* Weekly Goal - Removed for MVP */}
                {/*
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    Weekly Goal
                  </h3>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Progress</span>
                      <span className="text-sm font-semibold text-gray-900 dark:text-white">
                        {totalWatchTimeHours.toFixed(1)}h / 5h (Example Goal)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4">
                      <div
                        className="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full transition-all duration-500"
                        style={{ width: `${Math.min((totalWatchTimeHours / 5) * 100, 100)}%` }} // Example goal of 5 hours
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {totalWatchTimeHours >= 5 ? 'Goal achieved! 🎉' : `${(5 - totalWatchTimeHours).toFixed(1)}h remaining`}
                      </span>
                    </div>
                  </div>
                </div>
                */}

                {/* Achievements */}
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                    Achievements
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    {achievements.map((achievement) => {
                      const Icon = achievement.icon;
                      return (
                        <div
                          key={achievement.id}
                          className={`
                            flex items-center gap-2 p-2 rounded-md transition-all duration-200 group hover:scale-[1.01]
                            ${achievement.earned 
                              ? 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700' 
                              : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'
                            }
                          `}
                        >
                          <div className={`
                            w-6 h-6 rounded flex items-center justify-center flex-shrink-0 transition-transform duration-200 group-hover:scale-105
                            ${achievement.earned 
                              ? `bg-gradient-to-r ${achievement.color}` 
                              : 'bg-gray-200 dark:bg-gray-600'
                            }
                          `}>
                            <Icon className={`
                              h-3 w-3 transition-colors duration-200
                              ${achievement.earned ? 'text-white' : 'text-gray-400 dark:text-gray-500'}
                            `} />
                          </div>
                          <div className="flex-1 min-w-0 flex items-center justify-between">
                            <h4 className={`
                              font-medium text-xs
                              ${achievement.earned 
                                ? 'text-yellow-800 dark:text-yellow-200' 
                                : 'text-gray-600 dark:text-gray-400'
                              }
                            `}>
                              {achievement.title}
                            </h4>
                            {achievement.earned && (
                              <Award className="h-3 w-3 text-yellow-500 flex-shrink-0" />
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Quick Stats Card */}
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                    Quick Stats
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                      <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                        {Math.round(totalWatchTimeHours * 10) / 10}h
                      </div>
                      <div className="text-xs text-blue-600 dark:text-blue-400">Watch Time</div>
                    </div>
                    <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-md">
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">
                        {Math.round((stats.completedVideos / Math.max(stats.totalVideos, 1)) * 100)}%
                      </div>
                      <div className="text-xs text-green-600 dark:text-green-400">Completion</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mt-12">
              <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-3xl p-8 text-white shadow-2xl">
                <div className="flex flex-col md:flex-row items-center justify-between">
                  <div className="mb-6 md:mb-0">
                    <h3 className="text-2xl font-bold mb-2">Ready to learn something new?</h3>
                    <p className="text-purple-100">
                      Add a video and let our AI create summaries and quizzes for you
                    </p>
                  </div>
                  <div className="flex gap-4">
                    <button className="bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-2xl hover:bg-white/30 transition-all duration-200 flex items-center gap-2">
                      <Brain className="h-5 w-5" />
                      Browse Library
                    </button>
                    <button className="bg-white text-purple-600 px-6 py-3 rounded-2xl hover:bg-gray-100 transition-all duration-200 font-semibold flex items-center gap-2">
                      <Play className="h-5 w-5" />
                      Add Video
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}