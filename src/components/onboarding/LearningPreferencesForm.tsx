import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>rendingUp, 
  <PERSON><PERSON>, 
  <PERSON>riefcase, 
  Clock,
  ChevronRight,
  Check,
  Star,
  Target,
  Brain,
  Eye,
  Volume2,
  Hand
} from 'lucide-react';
import { UserLearningPreferences, LearningCategory } from '../../types';

interface LearningPreferencesFormProps {
  onComplete: (preferences: UserLearningPreferences) => void;
  onSkip: () => void;
}

export function LearningPreferencesForm({ onComplete, onSkip }: LearningPreferencesFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [preferences, setPreferences] = useState<Partial<UserLearningPreferences>>({
    categories: [],
    difficulty: 'beginner',
    timeCommitment: 5,
    learningStyle: 'mixed',
    goals: [],
    experience: {}
  });

  const categories: LearningCategory[] = [
    {
      id: 'software-development',
      name: 'Software Development',
      description: 'Programming, web development, mobile apps',
      icon: 'code',
      subcategories: ['JavaScript', 'Python', 'React', 'Node.js'],
      popularPaths: [],
      skillLevels: []
    },
    {
      id: 'data-science',
      name: 'Data Science',
      description: 'Data analysis, machine learning, AI',
      icon: 'bar-chart',
      subcategories: ['Python', 'Machine Learning', 'Statistics'],
      popularPaths: [],
      skillLevels: []
    },
    {
      id: 'digital-marketing',
      name: 'Digital Marketing',
      description: 'SEO, social media, content marketing',
      icon: 'trending-up',
      subcategories: ['SEO', 'Social Media', 'Analytics'],
      popularPaths: [],
      skillLevels: []
    },
    {
      id: 'design',
      name: 'Design',
      description: 'UI/UX, graphic design, creative tools',
      icon: 'palette',
      subcategories: ['UI/UX', 'Figma', 'Adobe Creative'],
      popularPaths: [],
      skillLevels: []
    },
    {
      id: 'business',
      name: 'Business',
      description: 'Entrepreneurship, strategy, leadership',
      icon: 'briefcase',
      subcategories: ['Strategy', 'Leadership', 'Finance'],
      popularPaths: [],
      skillLevels: []
    },
    {
      id: 'productivity',
      name: 'Productivity',
      description: 'Time management, personal development',
      icon: 'clock',
      subcategories: ['Time Management', 'Goal Setting'],
      popularPaths: [],
      skillLevels: []
    }
  ];

  const learningStyles = [
    {
      id: 'visual',
      name: 'Visual Learner',
      description: 'Learn best through images, diagrams, and visual content',
      icon: Eye
    },
    {
      id: 'auditory',
      name: 'Auditory Learner',
      description: 'Learn best through listening and verbal instruction',
      icon: Volume2
    },
    {
      id: 'kinesthetic',
      name: 'Kinesthetic Learner',
      description: 'Learn best through hands-on practice and doing',
      icon: Hand
    },
    {
      id: 'mixed',
      name: 'Mixed Learning',
      description: 'Combination of visual, auditory, and hands-on learning',
      icon: Brain
    }
  ];

  const commonGoals = [
    'Career advancement',
    'Skill development',
    'Personal interest',
    'Academic requirements',
    'Professional certification',
    'Starting a business',
    'Changing careers',
    'Staying current with trends'
  ];

  const getIconComponent = (iconName: string) => {
    const icons: Record<string, React.ComponentType<any>> = {
      'code': Code,
      'bar-chart': BarChart,
      'trending-up': TrendingUp,
      'palette': Palette,
      'briefcase': Briefcase,
      'clock': Clock
    };
    return icons[iconName] || Code;
  };

  const handleCategoryToggle = (categoryId: string) => {
    const currentCategories = preferences.categories || [];
    const newCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter(id => id !== categoryId)
      : [...currentCategories, categoryId];
    
    setPreferences(prev => ({ ...prev, categories: newCategories }));
  };

  const handleGoalToggle = (goal: string) => {
    const currentGoals = preferences.goals || [];
    const newGoals = currentGoals.includes(goal)
      ? currentGoals.filter(g => g !== goal)
      : [...currentGoals, goal];
    
    setPreferences(prev => ({ ...prev, goals: newGoals }));
  };

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(preferences as UserLearningPreferences);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: return (preferences.categories?.length || 0) > 0;
      case 1: return preferences.difficulty !== undefined;
      case 2: return preferences.timeCommitment !== undefined;
      case 3: return preferences.learningStyle !== undefined;
      case 4: return (preferences.goals?.length || 0) > 0;
      default: return true;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                What would you like to learn?
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Select the categories that interest you most
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {categories.map((category) => {
                const IconComponent = getIconComponent(category.icon);
                const isSelected = preferences.categories?.includes(category.id);
                
                return (
                  <button
                    key={category.id}
                    onClick={() => handleCategoryToggle(category.id)}
                    className={`
                      p-6 rounded-xl border-2 text-left transition-all duration-200 hover:scale-105
                      ${isSelected
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }
                    `}
                  >
                    <div className="flex items-start gap-4">
                      <div className={`
                        w-12 h-12 rounded-lg flex items-center justify-center
                        ${isSelected ? 'bg-blue-500' : 'bg-gray-100 dark:bg-gray-700'}
                      `}>
                        <IconComponent className={`h-6 w-6 ${isSelected ? 'text-white' : 'text-gray-600 dark:text-gray-400'}`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {category.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {category.subcategories.slice(0, 3).map((sub) => (
                            <span
                              key={sub}
                              className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded"
                            >
                              {sub}
                            </span>
                          ))}
                        </div>
                      </div>
                      {isSelected && (
                        <Check className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                What's your experience level?
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                This helps us recommend appropriate content
              </p>
            </div>
            
            <div className="space-y-4">
              {[
                { value: 'beginner', label: 'Beginner', description: 'New to these topics, looking to learn fundamentals' },
                { value: 'intermediate', label: 'Intermediate', description: 'Some experience, want to deepen knowledge' },
                { value: 'advanced', label: 'Advanced', description: 'Experienced, looking for specialized or cutting-edge content' }
              ].map((level) => (
                <button
                  key={level.value}
                  onClick={() => setPreferences(prev => ({ ...prev, difficulty: level.value as any }))}
                  className={`
                    w-full p-4 rounded-xl border-2 text-left transition-all duration-200
                    ${preferences.difficulty === level.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }
                  `}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {level.label}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {level.description}
                      </p>
                    </div>
                    {preferences.difficulty === level.value && (
                      <Check className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                How much time can you dedicate?
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Hours per week for learning
              </p>
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  {preferences.timeCommitment} hours
                </div>
                <input
                  type="range"
                  min="1"
                  max="20"
                  value={preferences.timeCommitment}
                  onChange={(e) => setPreferences(prev => ({ ...prev, timeCommitment: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-2">
                  <span>1 hour</span>
                  <span>20+ hours</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                {[
                  { range: '1-3', label: 'Casual', description: 'Light learning in spare time' },
                  { range: '4-8', label: 'Regular', description: 'Consistent skill building' },
                  { range: '9+', label: 'Intensive', description: 'Focused career development' }
                ].map((option) => (
                  <div
                    key={option.range}
                    className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {option.label}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {option.range} hours/week
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {option.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                How do you learn best?
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Choose your preferred learning style
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {learningStyles.map((style) => {
                const IconComponent = style.icon;
                const isSelected = preferences.learningStyle === style.id;
                
                return (
                  <button
                    key={style.id}
                    onClick={() => setPreferences(prev => ({ ...prev, learningStyle: style.id as any }))}
                    className={`
                      p-6 rounded-xl border-2 text-left transition-all duration-200
                      ${isSelected
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }
                    `}
                  >
                    <div className="flex items-start gap-4">
                      <div className={`
                        w-12 h-12 rounded-lg flex items-center justify-center
                        ${isSelected ? 'bg-blue-500' : 'bg-gray-100 dark:bg-gray-700'}
                      `}>
                        <IconComponent className={`h-6 w-6 ${isSelected ? 'text-white' : 'text-gray-600 dark:text-gray-400'}`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                          {style.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {style.description}
                        </p>
                      </div>
                      {isSelected && (
                        <Check className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                What are your learning goals?
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Select all that apply
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {commonGoals.map((goal) => {
                const isSelected = preferences.goals?.includes(goal);
                
                return (
                  <button
                    key={goal}
                    onClick={() => handleGoalToggle(goal)}
                    className={`
                      p-4 rounded-lg border-2 text-left transition-all duration-200
                      ${isSelected
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }
                    `}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-white">
                        {goal}
                      </span>
                      {isSelected && (
                        <Check className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 w-full max-w-4xl">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Step {currentStep + 1} of 5
            </span>
            <button
              onClick={onSkip}
              className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Skip for now
            </button>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / 5) * 100}%` }}
            />
          </div>
        </div>

        {/* Step Content */}
        <div className="mb-8">
          {renderStep()}
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <button
            onClick={handleBack}
            disabled={currentStep === 0}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            Back
          </button>
          
          <button
            onClick={handleNext}
            disabled={!canProceed()}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors duration-200 flex items-center gap-2"
          >
            {currentStep === 4 ? 'Complete Setup' : 'Next'}
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}