import React, { useState } from 'react';
import { AppProvider, useApp } from './contexts/AppContext';
import { LoginForm } from './components/auth/LoginForm';
import { LandingPage } from './components/landing/LandingPage';
import { Dashboard } from './components/dashboard/Dashboard';
import { Sidebar } from './components/layout/Sidebar';
import { VideoGrid } from './components/videos/VideoGrid';
import { AddVideoForm } from './components/videos/AddVideoForm';
import { PlaylistsView } from './components/playlists/PlaylistsView';
import { ProgressView } from './components/progress/ProgressView';
import { SettingsView } from './components/settings/SettingsView';
import { VideoPlayer } from './components/videos/VideoPlayer';
import { DemoPage } from './components/demo/DemoPage';
import { LearningPreferencesForm } from './components/onboarding/LearningPreferencesForm';
import { LearningPathsView } from './components/learning-paths/LearningPathsView';

function AppContent() {
  const { state } = useApp();
  const [currentView, setCurrentView] = useState('dashboard');
  const [showAddVideo, setShowAddVideo] = useState(false);
  const [showLanding, setShowLanding] = useState(!state.isAuthenticated);
  const [showDemo, setShowDemo] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarPinned, setSidebarPinned] = useState(true);

  // Check if user needs onboarding
  React.useEffect(() => {
    if (state.isAuthenticated && state.user && !state.user.preferences.hasCompletedOnboarding) {
      setShowOnboarding(true);
    }
  }, [state.isAuthenticated, state.user]);

  // Show login form if not authenticated
  if (!state.isAuthenticated && !showLanding) {
    return <LoginForm onBackToHome={() => setShowLanding(true)} />;
  }

  // Show onboarding if needed
  if (showOnboarding) {
    return (
      <LearningPreferencesForm
        onComplete={(preferences) => {
          // Save preferences and mark onboarding as complete
          dispatch({
            type: 'UPDATE_PREFERENCES',
            payload: { 
              ...preferences,
              hasCompletedOnboarding: true 
            }
          });
          setShowOnboarding(false);
        }}
        onSkip={() => {
          dispatch({
            type: 'UPDATE_PREFERENCES',
            payload: { hasCompletedOnboarding: true }
          });
          setShowOnboarding(false);
        }}
      />
    );
  }

  // Show landing page
  if (showLanding && !state.isAuthenticated) {
    return (
      <LandingPage 
        onGetStarted={() => {
          setShowLanding(false);
        }} 
        onWatchDemo={() => {
          setShowDemo(true);
          setShowLanding(false);
        }}
      />
    );
  }

  // Show demo page
  if (showDemo) {
    return <DemoPage onBack={() => setShowDemo(false)} />;
  }

  // Show video player if a video is selected
  if (state.currentVideo) {
    return <VideoPlayer />;
  }

  const renderMainContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard />;
      case 'videos':
        return <VideoGrid />;
      case 'learning-paths':
        return <LearningPathsView />;
      case 'playlists':
        return <PlaylistsView />;
      case 'progress':
        return <ProgressView />;
      case 'settings':
        return <SettingsView />;
      case 'add-video':
        setShowAddVideo(true);
        setCurrentView('videos');
        return <VideoGrid />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className={`
      flex min-h-screen
      ${state.user?.preferences.darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}
    `}>
      <Sidebar 
        currentView={currentView} 
        onViewChange={setCurrentView}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        isPinned={sidebarPinned}
        onTogglePin={() => setSidebarPinned(!sidebarPinned)}
      />
      
      <main className={`
        flex-1 overflow-y-auto transition-all duration-300
        ${sidebarCollapsed ? 'ml-16' : 'ml-64'}
      `}>
        {renderMainContent()}
      </main>

      {showAddVideo && (
        <AddVideoForm onClose={() => setShowAddVideo(false)} />
      )}
    </div>
  );
}

function App() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
}

export default App;