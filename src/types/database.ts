export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          name: string;
          points: number;
          preferences: {
            darkMode: boolean;
            focusMode: boolean;
            autoplay: boolean;
            notifications: boolean;
            openaiApiKey?: string;
            openaiModel: string;
            openaiMaxTokens: number;
            perplexityApiKey?: string;
            perplexityModel?: string;
            hasCompletedOnboarding?: boolean;
            learningPreferences?: any;
          };
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          name: string;
          points?: number;
          preferences?: {
            darkMode?: boolean;
            focusMode?: boolean;
            autoplay?: boolean;
            notifications?: boolean;
            openaiApiKey?: string;
            openaiModel?: string;
            openaiMaxTokens?: number;
          };
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          points?: number;
          preferences?: {
            darkMode?: boolean;
            focusMode?: boolean;
            autoplay?: boolean;
            notifications?: boolean;
            openai<PERSON><PERSON><PERSON>ey?: string;
            openaiModel?: string;
            openaiMaxTokens?: number;
          };
          updated_at?: string;
        };
      };
      videos: {
        Row: {
          id: string;
          user_id: string;
          url: string;
          title: string;
          description: string;
          thumbnail: string | null;
          duration: number;
          tags: string[];
          playlist_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          url: string;
          title: string;
          description?: string;
          thumbnail?: string | null;
          duration?: number;
          tags?: string[];
          playlist_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          url?: string;
          title?: string;
          description?: string;
          thumbnail?: string | null;
          duration?: number;
          tags?: string[];
          playlist_id?: string | null;
          updated_at?: string;
        };
      };
      playlists: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string;
          color: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          description?: string;
          color?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          description?: string;
          color?: string;
          updated_at?: string;
        };
      };
      video_progress: {
        Row: {
          id: string;
          user_id: string;
          video_id: string;
          status: 'not_started' | 'in_progress' | 'completed';
          watch_time: number;
          completion_percentage: number;
          last_watched: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          video_id: string;
          status?: 'not_started' | 'in_progress' | 'completed';
          watch_time?: number;
          completion_percentage?: number;
          last_watched?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          video_id?: string;
          status?: 'not_started' | 'in_progress' | 'completed';
          watch_time?: number;
          completion_percentage?: number;
          last_watched?: string;
          updated_at?: string;
        };
      };
      ai_summaries: {
        Row: {
          id: string;
          video_id: string;
          content: string;
          key_points: string[];
          generated_at: string;
        };
        Insert: {
          id?: string;
          video_id: string;
          content: string;
          key_points?: string[];
          generated_at?: string;
        };
        Update: {
          id?: string;
          video_id?: string;
          content?: string;
          key_points?: string[];
        };
      };
      quizzes: {
        Row: {
          id: string;
          video_id: string;
          questions: {
            id: string;
            question: string;
            options: string[];
            correctAnswer: number;
            explanation: string;
          }[];
          created_at: string;
        };
        Insert: {
          id?: string;
          video_id: string;
          questions: {
            id: string;
            question: string;
            options: string[];
            correctAnswer: number;
            explanation: string;
          }[];
          created_at?: string;
        };
        Update: {
          id?: string;
          video_id?: string;
          questions?: {
            id: string;
            question: string;
            options: string[];
            correctAnswer: number;
            explanation: string;
          }[];
        };
      };
      quiz_attempts: {
        Row: {
          id: string;
          user_id: string;
          quiz_id: string;
          user_answers: {
            questionId: string;
            selectedAnswer: number;
            isCorrect: boolean;
          }[];
          score: number;
          completed_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          quiz_id: string;
          user_answers: {
            questionId: string;
            selectedAnswer: number;
            isCorrect: boolean;
          }[];
          score?: number;
          completed_at?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          quiz_id?: string;
          user_answers?: {
            questionId: string;
            selectedAnswer: number;
            isCorrect: boolean;
          }[];
          score?: number;
          completed_at?: string;
        };
      };
      user_badges: {
        Row: {
          id: string;
          user_id: string;
          badge_name: string;
          badge_description: string;
          badge_icon: string;
          badge_category: 'progress' | 'quiz' | 'streak' | 'time';
          earned_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          badge_name: string;
          badge_description: string;
          badge_icon: string;
          badge_category: 'progress' | 'quiz' | 'streak' | 'time';
          earned_at?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          badge_name?: string;
          badge_description?: string;
          badge_icon?: string;
          badge_category?: 'progress' | 'quiz' | 'streak' | 'time';
          earned_at?: string;
        };
      };
    };
  };
}