export interface User {
  id: string;
  email: string;
  name: string;
  points: number;
  badges: Badge[];
  preferences: UserPreferences;
  createdAt: Date;
}

export interface UserPreferences {
  darkMode: boolean;
  focusMode: boolean;
  autoplay: boolean;
  notifications: boolean;
  openaiApiKey?: string;
  openaiModel: string;
  openaiMaxTokens: number;
  perplexityApiKey?: string;
  perplexityModel?: string;
  hasCompletedOnboarding?: boolean;
  learningPreferences?: UserLearningPreferences;
}

export interface Video {
  id: string;
  url: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: number;
  addedAt: Date;
  tags: string[];
  playlist?: string;
  progress: VideoProgress;
  summary?: AISummary;
  quiz?: Quiz;
}

export interface VideoProgress {
  status: 'not_started' | 'in_progress' | 'completed';
  watchTime: number;
  lastWatched: Date;
  completionPercentage: number;
}

export interface AISummary {
  id: string;
  content: string;
  keyPoints: string[];
  generatedAt: Date;
}

export interface Quiz {
  id: string;
  questions: Question[];
  userAnswers?: UserAnswer[];
  score?: number;
  completedAt?: Date;
}

export interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
}

export interface UserAnswer {
  questionId: string;
  selectedAnswer: number;
  isCorrect: boolean;
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  earnedAt: Date;
  category: 'progress' | 'quiz' | 'streak' | 'time';
}

export interface Playlist {
  id: string;
  name: string;
  description: string;
  videoIds: string[];
  createdAt: Date;
  color: string;
}

export interface LessonPlan {
  id: string;
  title: string;
  description: string;
  estimatedTime: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  videos: string[];
  prerequisites: string[];
  learningObjectives: string[];
  generatedAt: Date;
}

export interface LearningPath {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number; // in hours
  videos: RecommendedVideo[];
  prerequisites: string[];
  learningObjectives: string[];
  tags: string[];
  createdAt: Date;
  isPopular?: boolean;
  enrollmentCount?: number;
}

export interface RecommendedVideo {
  id: string;
  title: string;
  url: string;
  description: string;
  thumbnail: string;
  duration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  topics: string[];
  order: number;
  isRequired: boolean;
}

export interface UserLearningPreferences {
  categories: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  timeCommitment: number; // hours per week
  learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'mixed';
  goals: string[];
  experience: Record<string, 'none' | 'beginner' | 'intermediate' | 'advanced'>;
}

export interface LearningCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  subcategories: string[];
  popularPaths: string[];
  skillLevels: string[];
}</parameter>