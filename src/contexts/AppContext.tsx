import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { auth } from '../lib/supabase';
import { databaseService } from '../services/database';
import { User, Video, Playlist, UserPreferences } from '../types';

interface AppState {
  user: User | null;
  videos: Video[];
  playlists: Playlist[];
  currentVideo: Video | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  isLoading: boolean;
}

type AppAction =
  | { type: 'SET_USER'; payload: User }
  | { type: 'LOGOUT' }
  | { type: 'ADD_VIDEO'; payload: Video }
  | { type: 'UPDATE_VIDEO'; payload: Video }
  | { type: 'DELETE_VIDEO'; payload: string }
  | { type: 'SET_CURRENT_VIDEO'; payload: Video | null }
  | { type: 'ADD_PLAYLIST'; payload: Playlist }
  | { type: 'UPDATE_PLAYLIST'; payload: Playlist }
  | { type: 'DELETE_PLAYLIST'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<UserPreferences> }
  | { type: 'SET_VIDEOS'; payload: Video[] }
  | { type: 'SET_PLAYLISTS'; payload: Playlist[] }
  | { type: 'SET_LOADING'; payload: boolean };

const initialState: AppState = {
  user: null,
  videos: [],
  playlists: [],
  currentVideo: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        error: null,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        videos: [],
        playlists: [],
        isAuthenticated: false,
        currentVideo: null,
      };
    case 'ADD_VIDEO':
      return {
        ...state,
        videos: [...state.videos, action.payload],
      };
    case 'UPDATE_VIDEO':
      return {
        ...state,
        videos: state.videos.map(video =>
          video.id === action.payload.id ? action.payload : video
        ),
      };
    case 'DELETE_VIDEO':
      return {
        ...state,
        videos: state.videos.filter(video => video.id !== action.payload),
      };
    case 'SET_CURRENT_VIDEO':
      return {
        ...state,
        currentVideo: action.payload,
      };
    case 'ADD_PLAYLIST':
      return {
        ...state,
        playlists: [...state.playlists, action.payload],
      };
    case 'UPDATE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.map(playlist =>
          playlist.id === action.payload.id ? action.payload : playlist
        ),
      };
    case 'DELETE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.filter(playlist => playlist.id !== action.payload),
      };
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        user: state.user ? {
          ...state.user,
          preferences: {
            ...state.user.preferences,
            ...action.payload,
          },
        } : null,
      };
    case 'SET_VIDEOS':
      return {
        ...state,
        videos: action.payload,
      };
    case 'SET_PLAYLISTS':
      return {
        ...state,
        playlists: action.payload,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Initialize auth state and load user data
  useEffect(() => {
    const initializeAuth = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      try {
        // Check current auth state
        const { user: authUser } = await auth.getCurrentUser();
        
        if (authUser) {
          // Load user profile and data
          const [user, videos, playlists] = await Promise.all([
            databaseService.getCurrentUser(),
            databaseService.getUserVideos(),
            databaseService.getUserPlaylists(),
          ]);
          
          if (user) {
            dispatch({ type: 'SET_USER', payload: user });
            dispatch({ type: 'SET_VIDEOS', payload: videos });
            dispatch({ type: 'SET_PLAYLISTS', payload: playlists });
          }
        } else {
          // Try to load demo data from localStorage for non-authenticated users
          try {
            const savedUser = localStorage.getItem('amiLearning_user');
            if (savedUser) {
              dispatch({ type: 'SET_USER', payload: JSON.parse(savedUser) });
            }
          } catch (error) {
            console.error('Error loading demo data:', error);
          }
          
          try {
            const savedVideos = localStorage.getItem('amiLearning_videos');
            if (savedVideos) {
              dispatch({ type: 'SET_VIDEOS', payload: JSON.parse(savedVideos) });
            }
          } catch (error) {
            console.error('Error loading demo videos:', error);
          }
          
          try {
            const savedPlaylists = localStorage.getItem('amiLearning_playlists');
            if (savedPlaylists) {
              dispatch({ type: 'SET_PLAYLISTS', payload: JSON.parse(savedPlaylists) });
            }
          } catch (error) {
            console.error('Error loading demo playlists:', error);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load user data' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeAuth();

    // Listen for auth state changes
    const { data: { subscription } } = auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        // User signed in, load their data
        try {
          const [user, videos, playlists] = await Promise.all([
            databaseService.getCurrentUser(),
            databaseService.getUserVideos(),
            databaseService.getUserPlaylists(),
          ]);
          
          if (user) {
            dispatch({ type: 'SET_USER', payload: user });
            dispatch({ type: 'SET_VIDEOS', payload: videos });
            dispatch({ type: 'SET_PLAYLISTS', payload: playlists });
          }
        } catch (error) {
          console.error('Error loading user data after sign in:', error);
          dispatch({ type: 'SET_ERROR', payload: 'Failed to load user data' });
        }
      } else if (event === 'SIGNED_OUT') {
        // User signed out, clear data
        dispatch({ type: 'LOGOUT' });
        // Clear localStorage
        localStorage.removeItem('amiLearning_user');
        localStorage.removeItem('amiLearning_videos');
        localStorage.removeItem('amiLearning_playlists');
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Save demo data to localStorage for non-authenticated users
  useEffect(() => {
    if (state.user && !state.isAuthenticated) {
      localStorage.setItem('amiLearning_user', JSON.stringify(state.user));
    } else {
      localStorage.removeItem('amiLearning_user');
    }
  }, [state.user]);

  useEffect(() => {
    if (!state.isAuthenticated) {
      localStorage.setItem('amiLearning_videos', JSON.stringify(state.videos));
    }
  }, [state.videos]);

  useEffect(() => {
    if (!state.isAuthenticated) {
      localStorage.setItem('amiLearning_playlists', JSON.stringify(state.playlists));
    }
  }, [state.playlists]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}