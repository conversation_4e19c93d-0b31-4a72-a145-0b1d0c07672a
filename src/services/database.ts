import { supabase } from '../lib/supabase';
import { User, Video, Playlist, VideoProgress, AISummary, Quiz, UserAnswer, Badge } from '../types';
import { Database } from '../types/database';

type DbProfile = Database['public']['Tables']['profiles']['Row'];
type DbVideo = Database['public']['Tables']['videos']['Row'];
type DbPlaylist = Database['public']['Tables']['playlists']['Row'];
type DbVideoProgress = Database['public']['Tables']['video_progress']['Row'];
type DbAISummary = Database['public']['Tables']['ai_summaries']['Row'];
type DbQuiz = Database['public']['Tables']['quizzes']['Row'];
type DbQuizAttempt = Database['public']['Tables']['quiz_attempts']['Row'];
type DbUserBadge = Database['public']['Tables']['user_badges']['Row'];

// Helper functions to convert between database and app types
const dbProfileToUser = (profile: DbProfile, badges: Badge[] = []): User => ({
  id: profile.id,
  email: profile.email,
  name: profile.name,
  points: profile.points,
  badges,
  preferences: {
    darkMode: profile.preferences.darkMode || false,
    focusMode: profile.preferences.focusMode || false,
    autoplay: profile.preferences.autoplay !== false,
    notifications: profile.preferences.notifications !== false,
    openaiApiKey: profile.preferences.openaiApiKey,
    openaiModel: profile.preferences.openaiModel || 'gpt-3.5-turbo',
    openaiMaxTokens: profile.preferences.openaiMaxTokens || 500,
  },
  createdAt: new Date(profile.created_at),
});

const dbVideoToVideo = (
  video: DbVideo, 
  progress?: DbVideoProgress, 
  summary?: DbAISummary, 
  quiz?: DbQuiz,
  quizAttempt?: DbQuizAttempt
): Video => ({
  id: video.id,
  url: video.url,
  title: video.title,
  description: video.description,
  thumbnail: video.thumbnail || '',
  duration: video.duration,
  addedAt: new Date(video.created_at),
  tags: video.tags,
  playlist: video.playlist_id || undefined,
  progress: progress ? {
    status: progress.status,
    watchTime: progress.watch_time,
    lastWatched: new Date(progress.last_watched),
    completionPercentage: progress.completion_percentage,
  } : {
    status: 'not_started',
    watchTime: 0,
    lastWatched: new Date(),
    completionPercentage: 0,
  },
  summary: summary ? {
    id: summary.id,
    content: summary.content,
    keyPoints: summary.key_points,
    generatedAt: new Date(summary.generated_at),
  } : undefined,
  quiz: quiz ? {
    id: quiz.id,
    questions: quiz.questions,
    userAnswers: quizAttempt?.user_answers,
    score: quizAttempt?.score,
    completedAt: quizAttempt ? new Date(quizAttempt.completed_at) : undefined,
  } : undefined,
});

const dbPlaylistToPlaylist = (playlist: DbPlaylist, videoIds: string[] = []): Playlist => ({
  id: playlist.id,
  name: playlist.name,
  description: playlist.description,
  videoIds,
  createdAt: new Date(playlist.created_at),
  color: playlist.color,
});

const dbBadgeToBadge = (badge: DbUserBadge): Badge => ({
  id: badge.id,
  name: badge.badge_name,
  description: badge.badge_description,
  icon: badge.badge_icon,
  earnedAt: new Date(badge.earned_at),
  category: badge.badge_category,
});

// Database service functions
export const databaseService = {
  // User/Profile operations
  async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;

    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (!profile) return null;

    const { data: badges } = await supabase
      .from('user_badges')
      .select('*')
      .eq('user_id', user.id);

    return dbProfileToUser(profile, badges?.map(dbBadgeToBadge) || []);
  },

  async updateUserProfile(updates: Partial<User>): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const dbUpdates: any = {};
    if (updates.name) dbUpdates.name = updates.name;
    if (updates.points !== undefined) dbUpdates.points = updates.points;
    if (updates.preferences) dbUpdates.preferences = updates.preferences;

    const { error } = await supabase
      .from('profiles')
      .update(dbUpdates)
      .eq('id', user.id);

    if (error) throw error;
  },

  // Video operations
  async getUserVideos(): Promise<Video[]> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    const { data: videos, error } = await supabase
      .from('videos')
      .select(`
        *,
        video_progress(*),
        ai_summaries(*),
        quizzes(*),
        quiz_attempts(*)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return videos?.map(video => dbVideoToVideo(
      video,
      video.video_progress?.[0],
      video.ai_summaries?.[0],
      video.quizzes?.[0],
      video.quiz_attempts?.[0]
    )) || [];
  },

  async addVideo(video: Omit<Video, 'id' | 'addedAt'>): Promise<Video> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // Insert video
    const { data: newVideo, error: videoError } = await supabase
      .from('videos')
      .insert({
        user_id: user.id,
        url: video.url,
        title: video.title,
        description: video.description,
        thumbnail: video.thumbnail,
        duration: video.duration,
        tags: video.tags,
        playlist_id: video.playlist || null,
      })
      .select()
      .single();

    if (videoError) throw videoError;

    // Insert progress
    const { error: progressError } = await supabase
      .from('video_progress')
      .insert({
        user_id: user.id,
        video_id: newVideo.id,
        status: video.progress.status,
        watch_time: video.progress.watchTime,
        completion_percentage: video.progress.completionPercentage,
      });

    if (progressError) throw progressError;

    // Insert summary if provided
    if (video.summary) {
      const { error: summaryError } = await supabase
        .from('ai_summaries')
        .insert({
          video_id: newVideo.id,
          content: video.summary.content,
          key_points: video.summary.keyPoints,
        });

      if (summaryError) throw summaryError;
    }

    // Insert quiz if provided
    if (video.quiz) {
      const { data: newQuiz, error: quizError } = await supabase
        .from('quizzes')
        .insert({
          video_id: newVideo.id,
          questions: video.quiz.questions,
        })
        .select()
        .single();

      if (quizError) throw quizError;

      // Insert quiz attempt if completed
      if (video.quiz.userAnswers && video.quiz.score !== undefined) {
        const { error: attemptError } = await supabase
          .from('quiz_attempts')
          .insert({
            user_id: user.id,
            quiz_id: newQuiz.id,
            user_answers: video.quiz.userAnswers,
            score: video.quiz.score,
          });

        if (attemptError) throw attemptError;
      }
    }

    return dbVideoToVideo(newVideo);
  },

  async updateVideo(video: Video): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // Update video
    const { error: videoError } = await supabase
      .from('videos')
      .update({
        title: video.title,
        description: video.description,
        thumbnail: video.thumbnail,
        duration: video.duration,
        tags: video.tags,
        playlist_id: video.playlist || null,
      })
      .eq('id', video.id)
      .eq('user_id', user.id);

    if (videoError) throw videoError;

    // Update progress
    const { error: progressError } = await supabase
      .from('video_progress')
      .upsert({
        user_id: user.id,
        video_id: video.id,
        status: video.progress.status,
        watch_time: video.progress.watchTime,
        completion_percentage: video.progress.completionPercentage,
        last_watched: video.progress.lastWatched.toISOString(),
      });

    if (progressError) throw progressError;

    // Update quiz attempt if quiz is completed
    if (video.quiz?.userAnswers && video.quiz.score !== undefined) {
      const { data: quiz } = await supabase
        .from('quizzes')
        .select('id')
        .eq('video_id', video.id)
        .single();

      if (quiz) {
        const { error: attemptError } = await supabase
          .from('quiz_attempts')
          .upsert({
            user_id: user.id,
            quiz_id: quiz.id,
            user_answers: video.quiz.userAnswers,
            score: video.quiz.score,
            completed_at: video.quiz.completedAt?.toISOString() || new Date().toISOString(),
          });

        if (attemptError) throw attemptError;
      }
    }
  },

  async deleteVideo(videoId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('videos')
      .delete()
      .eq('id', videoId)
      .eq('user_id', user.id);

    if (error) throw error;
  },

  // Playlist operations
  async getUserPlaylists(): Promise<Playlist[]> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    const { data: playlists, error } = await supabase
      .from('playlists')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Get video IDs for each playlist
    const playlistsWithVideos = await Promise.all(
      (playlists || []).map(async (playlist) => {
        const { data: videos } = await supabase
          .from('videos')
          .select('id')
          .eq('playlist_id', playlist.id)
          .eq('user_id', user.id);

        return dbPlaylistToPlaylist(playlist, videos?.map(v => v.id) || []);
      })
    );

    return playlistsWithVideos;
  },

  async addPlaylist(playlist: Omit<Playlist, 'id' | 'createdAt' | 'videoIds'>): Promise<Playlist> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data: newPlaylist, error } = await supabase
      .from('playlists')
      .insert({
        user_id: user.id,
        name: playlist.name,
        description: playlist.description,
        color: playlist.color,
      })
      .select()
      .single();

    if (error) throw error;

    return dbPlaylistToPlaylist(newPlaylist);
  },

  async updatePlaylist(playlist: Playlist): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('playlists')
      .update({
        name: playlist.name,
        description: playlist.description,
        color: playlist.color,
      })
      .eq('id', playlist.id)
      .eq('user_id', user.id);

    if (error) throw error;
  },

  async deletePlaylist(playlistId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('playlists')
      .delete()
      .eq('id', playlistId)
      .eq('user_id', user.id);

    if (error) throw error;
  },

  // Badge operations
  async addUserBadge(badge: Omit<Badge, 'id' | 'earnedAt'>): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('user_badges')
      .insert({
        user_id: user.id,
        badge_name: badge.name,
        badge_description: badge.description,
        badge_icon: badge.icon,
        badge_category: badge.category,
      });

    if (error) throw error;
  },
};