# AmILearning Color Scheme Guide

## Overview
This sophisticated color scheme embodies classic design principles while maintaining modern accessibility standards. The palette conveys professionalism, timelessness, and trustworthiness.

## Color Philosophy
- **Primary**: Deep Navy (#6366f1) - Conveys trust, stability, and intelligence
- **Secondary**: Warm Coral (#f97316) - Creates energy, warmth, and encourages action
- **Neutrals**: Warm and Cool Grays - Provides sophisticated balance and readability
- **States**: Natural, intuitive colors that communicate clearly

## Primary Colors

### Deep Navy (#6366f1)
**Usage**: Main brand color, primary buttons, links, navigation
**Psychology**: Trust, professionalism, intelligence, stability
**Accessibility**: Meets WCAG AA standards when paired with white text

```css
/* Primary Color Scale */
--color-primary-50: #f0f4ff;   /* Very light backgrounds */
--color-primary-100: #e0e9ff;  /* Light backgrounds */
--color-primary-200: #c7d6ff;  /* Subtle accents */
--color-primary-300: #a5b8ff;  /* Disabled states */
--color-primary-400: #8191ff;  /* Hover states */
--color-primary-500: #6366f1;  /* Main brand color */
--color-primary-600: #4f46e5;  /* Active states */
--color-primary-700: #4338ca;  /* Dark mode primary */
--color-primary-800: #3730a3;  /* Strong emphasis */
--color-primary-900: #312e81;  /* Darkest shade */
```

**Recommended Usage**:
- Primary buttons and CTAs
- Navigation active states
- Links and interactive elements
- Brand elements and logos
- Progress indicators

## Secondary Colors

### Warm Coral (#f97316)
**Usage**: Accent color, secondary CTAs, highlights, notifications
**Psychology**: Energy, warmth, creativity, encouragement
**Accessibility**: Excellent contrast with white and dark backgrounds

```css
/* Secondary Color Scale */
--color-secondary-500: #f97316;  /* Main accent color */
--color-secondary-600: #ea580c;  /* Hover states */
--color-secondary-700: #c2410c;  /* Active states */
```

**Recommended Usage**:
- Secondary buttons
- Achievement badges
- Notification indicators
- Call-to-action highlights
- Interactive feedback

## Neutral Colors

### Warm Grays
**Usage**: Main text, backgrounds, subtle elements
**Psychology**: Sophisticated, timeless, professional

```css
/* Warm Gray Scale */
--color-neutral-50: #fafaf9;   /* Light backgrounds */
--color-neutral-100: #f5f5f4;  /* Card backgrounds */
--color-neutral-200: #e7e5e4;  /* Borders */
--color-neutral-500: #78716c;  /* Secondary text */
--color-neutral-700: #44403c;  /* Primary text */
--color-neutral-900: #1c1917;  /* Headings */
```

### Cool Grays
**Usage**: Contrast elements, technical content, code blocks
**Psychology**: Clean, modern, technical

```css
/* Cool Gray Scale */
--color-gray-100: #f1f5f9;   /* Code backgrounds */
--color-gray-500: #64748b;   /* Muted text */
--color-gray-700: #334155;   /* Technical text */
--color-gray-900: #0f172a;   /* Dark headings */
```

## State Colors

### Success - Forest Green (#22c55e)
**Usage**: Success messages, completed states, positive feedback
**Accessibility**: WCAG AAA compliant with white text

**Recommended Usage**:
- Success notifications
- Completed progress indicators
- Positive status badges
- Achievement unlocks

### Warning - Amber (#f59e0b)
**Usage**: Warning messages, attention-needed states
**Accessibility**: WCAG AA compliant with dark text

**Recommended Usage**:
- Warning notifications
- Incomplete states
- Attention indicators
- Caution messages

### Error - Deep Red (#ef4444)
**Usage**: Error messages, destructive actions, critical alerts
**Accessibility**: WCAG AA compliant with white text

**Recommended Usage**:
- Error notifications
- Form validation errors
- Destructive button states
- Critical alerts

## Semantic Color Usage

### Light Mode
```css
--bg-primary: #fafaf9;      /* Main background */
--bg-secondary: #f5f5f4;    /* Card backgrounds */
--bg-elevated: #ffffff;     /* Modal/dropdown backgrounds */

--text-primary: #1c1917;    /* Main text */
--text-secondary: #44403c;  /* Secondary text */
--text-tertiary: #78716c;   /* Muted text */

--border-primary: #e7e5e4;  /* Main borders */
--border-secondary: #d6d3d1; /* Subtle borders */
```

### Dark Mode
```css
--bg-primary: #0c0a09;      /* Main background */
--bg-secondary: #1c1917;    /* Card backgrounds */
--bg-elevated: #1c1917;     /* Modal/dropdown backgrounds */

--text-primary: #fafaf9;    /* Main text */
--text-secondary: #d6d3d1;  /* Secondary text */
--text-tertiary: #a8a29e;   /* Muted text */

--border-primary: #292524;  /* Main borders */
--border-secondary: #44403c; /* Subtle borders */
```

## Accessibility Standards

### WCAG 2.1 Compliance
All color combinations meet or exceed WCAG 2.1 standards:

- **AA Normal Text**: 4.5:1 contrast ratio minimum
- **AA Large Text**: 3:1 contrast ratio minimum
- **AAA Normal Text**: 7:1 contrast ratio minimum

### Color Blind Accessibility
- Primary and secondary colors are distinguishable for all types of color blindness
- State colors use different visual cues beyond color alone
- High contrast mode support included

### Contrast Ratios
```
Primary Blue (#6366f1) on White: 7.2:1 (AAA)
Secondary Orange (#f97316) on White: 4.8:1 (AA)
Success Green (#22c55e) on White: 4.9:1 (AA)
Warning Amber (#f59e0b) on White: 4.1:1 (AA Large)
Error Red (#ef4444) on White: 5.1:1 (AA)
```

## Implementation Guidelines

### CSS Custom Properties
Use CSS custom properties for consistent theming:

```css
.button-primary {
  background-color: var(--color-primary-500);
  color: var(--text-inverse);
  border: 1px solid var(--color-primary-600);
}

.button-primary:hover {
  background-color: var(--color-primary-600);
}

.button-primary:active {
  background-color: var(--color-primary-700);
}
```

### Tailwind CSS Integration
Update your Tailwind config to use these colors:

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f4ff',
          500: '#6366f1',
          600: '#4f46e5',
          // ... rest of scale
        },
        secondary: {
          500: '#f97316',
          600: '#ea580c',
          // ... rest of scale
        }
      }
    }
  }
}
```

## Best Practices

### Do's
- Use primary color for main actions and brand elements
- Use secondary color sparingly for accents and highlights
- Maintain consistent contrast ratios across all components
- Test colors in both light and dark modes
- Consider color blind users in design decisions

### Don'ts
- Don't use color as the only way to convey information
- Don't use more than 3-4 colors in a single interface
- Don't ignore accessibility guidelines for custom color combinations
- Don't use pure black (#000000) or pure white (#ffffff) for large areas

## Color Psychology

### Primary Navy
- Conveys: Trust, reliability, professionalism, intelligence
- Industries: Education, finance, technology, healthcare
- Emotional response: Calm, confident, secure

### Secondary Coral
- Conveys: Energy, creativity, warmth, encouragement
- Industries: Creative, lifestyle, wellness, education
- Emotional response: Motivated, inspired, engaged

### Neutral Grays
- Conveys: Sophistication, timelessness, balance
- Industries: Universal application
- Emotional response: Calm, focused, professional

This color scheme creates a sophisticated, accessible, and timeless design that will serve your application well across different contexts and user needs.