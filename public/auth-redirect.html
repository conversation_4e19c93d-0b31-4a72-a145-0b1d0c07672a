<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Confirmation - AmILearning</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success {
            color: #10b981;
            font-size: 18px;
            margin: 20px 0;
        }
        .error {
            color: #ef4444;
            font-size: 16px;
            margin: 20px 0;
        }
        .redirect-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            margin-top: 20px;
            transition: background 0.2s;
        }
        .redirect-link:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email Confirmation</h1>
        <div id="status">
            <div class="spinner"></div>
            <p>Processing your email confirmation...</p>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            if (type === 'loading') {
                statusDiv.innerHTML = `
                    <div class="spinner"></div>
                    <p>${message}</p>
                `;
            } else if (type === 'success') {
                statusDiv.innerHTML = `
                    <div class="success">✓ ${message}</div>
                    <a href="/" class="redirect-link">Go to Dashboard</a>
                `;
            } else if (type === 'error') {
                statusDiv.innerHTML = `
                    <div class="error">✗ ${message}</div>
                    <a href="/" class="redirect-link">Go to Home</a>
                `;
            }
        }

        function extractTokensFromUrl() {
            const url = window.location.href;
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            const searchParams = new URLSearchParams(window.location.search);
            
            return {
                access_token: hashParams.get('access_token') || searchParams.get('access_token'),
                refresh_token: hashParams.get('refresh_token') || searchParams.get('refresh_token'),
                expires_at: hashParams.get('expires_at') || searchParams.get('expires_at'),
                expires_in: hashParams.get('expires_in') || searchParams.get('expires_in'),
                token_type: hashParams.get('token_type') || searchParams.get('token_type'),
                type: hashParams.get('type') || searchParams.get('type'),
                error: hashParams.get('error') || searchParams.get('error'),
                error_description: hashParams.get('error_description') || searchParams.get('error_description')
            };
        }

        function redirectToApp(tokens) {
            // Determine the correct app URL
            let appUrl = 'http://localhost:5173'; // Default for development
            
            // Check if we're on a production domain
            if (window.location.hostname.includes('vercel.app')) {
                appUrl = 'https://amilearning-bolt.vercel.app';
            } else if (window.location.hostname.includes('netlify.app')) {
                appUrl = 'https://your-app.netlify.app';
            } else if (!window.location.hostname.includes('localhost')) {
                // Custom domain
                appUrl = `${window.location.protocol}//${window.location.hostname}`;
            }
            
            // Build the redirect URL with tokens
            const params = new URLSearchParams();
            Object.keys(tokens).forEach(key => {
                if (tokens[key]) {
                    params.set(key, tokens[key]);
                }
            });
            
            const redirectUrl = `${appUrl}/auth/callback?${params.toString()}`;
            
            updateStatus('Redirecting to your dashboard...', 'success');
            
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 2000);
        }

        // Main execution
        try {
            const tokens = extractTokensFromUrl();
            
            if (tokens.error) {
                updateStatus(`Authentication failed: ${tokens.error_description || tokens.error}`, 'error');
                return;
            }
            
            if (tokens.access_token && tokens.refresh_token) {
                updateStatus('Email confirmed successfully!', 'success');
                redirectToApp(tokens);
            } else {
                updateStatus('No authentication tokens found. Please try signing up again.', 'error');
            }
        } catch (error) {
            console.error('Error processing auth callback:', error);
            updateStatus('An error occurred while processing your confirmation.', 'error');
        }
    </script>
</body>
</html>
